/**
 * @file     PubsecConfig.h
 * @brief    公安视图库配置管理
 * <AUTHOR> @date     2024-01-20
 * @version  1.0
 * @copyright V1.0  Copyright(C) 2024 All rights reserved.
 */

#ifndef _INCLUDE_PUBSEC_CONFIG_H_
#define _INCLUDE_PUBSEC_CONFIG_H_
#pragma once

#include <string>
#include <memory>
#include "Json/Value.h"
#include "Json/Writer.h"
#include "Json/Reader.h"
#include "PubsecTypes.h"
#include "PubsecConstants.h"
#include "PubsecErrors.h"

namespace Uface
{
    namespace Application
    {
        /**
         * @brief 公安视图库配置结构
         */
        struct PubsecConfiguration {
            bool enable = false;                                    // 是否启用
            std::string username;                                   // 用户名
            std::string password;                                   // 密码
            std::string deviceId;                                   // 设备ID
            std::string platformAddress;                            // 平台地址
            uint16_t port = PUBSEC_DEFAULT_HTTP_PORT;              // 端口
            uint32_t heartbeatInterval = PUBSEC_DEFAULT_HEARTBEAT_INTERVAL; // 心跳间隔
            uint32_t syncInterval = PUBSEC_DEFAULT_SYNC_INTERVAL;   // 同步间隔
            uint32_t timeout = PUBSEC_DEFAULT_TIMEOUT;              // 超时时间
            uint32_t retryCount = PUBSEC_MAX_RETRY_COUNT;           // 重试次数
            EPubSecTransMethod transMethod = PUBSEC_TRANS_HTTP;     // 传输方法

            // 验证配置有效性
            bool isValid() const {
                return !username.empty() &&
                       !password.empty() &&
                       !deviceId.empty() &&
                       !platformAddress.empty() &&
                       port > 0 && port <= 65535 &&
                       heartbeatInterval > 0 &&
                       syncInterval > 0 &&
                       timeout > 0 &&
                       retryCount > 0;
            }

            // 获取完整的平台URL
            std::string getPlatformUrl() const {
                std::string protocol = (transMethod == PUBSEC_TRANS_HTTPS) ? "https://" : "http://";
                return protocol + platformAddress + ":" + std::to_string(port);
            }
        };

        /**
         * @brief 配置验证器接口
         */
        class IConfigValidator {
        public:
            virtual ~IConfigValidator() = default;
            virtual Result<bool> validate(const PubsecConfiguration& config) = 0;
        };

        /**
         * @brief 默认配置验证器
         */
        class DefaultConfigValidator : public IConfigValidator {
        public:
            Result<bool> validate(const PubsecConfiguration& config) override {
                // 检查必填字段
                if (config.username.empty()) {
                    return Result<bool>::error(PUBSEC_CLT_PARAM_INVALID, "Username is required");
                }

                if (config.password.empty()) {
                    return Result<bool>::error(PUBSEC_CLT_PARAM_INVALID, "Password is required");
                }

                if (config.deviceId.empty()) {
                    return Result<bool>::error(PUBSEC_CLT_PARAM_INVALID, "Device ID is required");
                }

                if (config.platformAddress.empty()) {
                    return Result<bool>::error(PUBSEC_CLT_PARAM_INVALID, "Platform address is required");
                }

                // 检查端口范围
                if (config.port == 0 || config.port > 65535) {
                    return Result<bool>::error(PUBSEC_CLT_PARAM_INVALID, "Invalid port number");
                }

                // 检查时间间隔
                if (config.heartbeatInterval == 0) {
                    return Result<bool>::error(PUBSEC_CLT_PARAM_INVALID, "Heartbeat interval must be greater than 0");
                }

                if (config.syncInterval == 0) {
                    return Result<bool>::error(PUBSEC_CLT_PARAM_INVALID, "Sync interval must be greater than 0");
                }

                if (config.timeout == 0) {
                    return Result<bool>::error(PUBSEC_CLT_PARAM_INVALID, "Timeout must be greater than 0");
                }

                // 检查设备ID长度
                if (config.deviceId.length() > PUBSEC_DEVICE_ID_LENGTH) {
                    return Result<bool>::error(PUBSEC_CLT_PARAM_INVALID, "Device ID too long");
                }

                // 检查用户名和密码长度
                if (config.username.length() > PUBSEC_USER_NAME_LENGTH) {
                    return Result<bool>::error(PUBSEC_CLT_PARAM_INVALID, "Username too long");
                }

                if (config.password.length() > PUBSEC_PASSWORD_LENGTH) {
                    return Result<bool>::error(PUBSEC_CLT_PARAM_INVALID, "Password too long");
                }

                return Result<bool>::success(true);
            }
        };

        /**
         * @brief 公安视图库配置管理器
         */
        class PubsecConfigManager {
        private:
            PubsecConfiguration config_;
            std::unique_ptr<IConfigValidator> validator_;
            bool loaded_;

        public:
            /**
             * @brief 构造函数
             * @param validator 配置验证器，如果为空则使用默认验证器
             */
            explicit PubsecConfigManager(std::unique_ptr<IConfigValidator> validator = nullptr)
                : validator_(validator ? std::move(validator) :
                           std::unique_ptr<IConfigValidator>(new DefaultConfigValidator()))
                , loaded_(false) {}

            /**
             * @brief 从JSON加载配置
             * @param json JSON配置对象
             * @return 加载结果
             */
            Result<bool> loadFromJson(const Json::Value& json) {
                try {
                    PubsecConfiguration newConfig;

                    // 解析配置项
                    if (json.isMember(PUBSEC_CONFIG_ENABLE) && json[PUBSEC_CONFIG_ENABLE].isBool()) {
                        newConfig.enable = json[PUBSEC_CONFIG_ENABLE].asBool();
                    }

                    if (json.isMember(PUBSEC_CONFIG_USERNAME) && json[PUBSEC_CONFIG_USERNAME].isString()) {
                        newConfig.username = json[PUBSEC_CONFIG_USERNAME].asString();
                    }

                    if (json.isMember(PUBSEC_CONFIG_PASSWORD) && json[PUBSEC_CONFIG_PASSWORD].isString()) {
                        newConfig.password = json[PUBSEC_CONFIG_PASSWORD].asString();
                    }

                    if (json.isMember(PUBSEC_CONFIG_DEVICE_ID) && json[PUBSEC_CONFIG_DEVICE_ID].isString()) {
                        newConfig.deviceId = json[PUBSEC_CONFIG_DEVICE_ID].asString();
                    }

                    if (json.isMember(PUBSEC_CONFIG_PLATFORM_ADDRESS) && json[PUBSEC_CONFIG_PLATFORM_ADDRESS].isString()) {
                        newConfig.platformAddress = json[PUBSEC_CONFIG_PLATFORM_ADDRESS].asString();
                    }

                    if (json.isMember(PUBSEC_CONFIG_VIDEO_LIB_PORT)) {
                        if (json[PUBSEC_CONFIG_VIDEO_LIB_PORT].isString()) {
                            newConfig.port = static_cast<uint16_t>(std::stoi(json[PUBSEC_CONFIG_VIDEO_LIB_PORT].asString()));
                        } else if (json[PUBSEC_CONFIG_VIDEO_LIB_PORT].isInt()) {
                            newConfig.port = static_cast<uint16_t>(json[PUBSEC_CONFIG_VIDEO_LIB_PORT].asInt());
                        }
                    }

                    if (json.isMember(PUBSEC_CONFIG_HEARTBEAT_INTERVAL) && json[PUBSEC_CONFIG_HEARTBEAT_INTERVAL].isInt()) {
                        newConfig.heartbeatInterval = static_cast<uint32_t>(json[PUBSEC_CONFIG_HEARTBEAT_INTERVAL].asInt());
                    }

                    if (json.isMember(PUBSEC_CONFIG_SYNC_INTERVAL) && json[PUBSEC_CONFIG_SYNC_INTERVAL].isInt()) {
                        newConfig.syncInterval = static_cast<uint32_t>(json[PUBSEC_CONFIG_SYNC_INTERVAL].asInt());
                    }

                    if (json.isMember(PUBSEC_CONFIG_TIMEOUT) && json[PUBSEC_CONFIG_TIMEOUT].isInt()) {
                        newConfig.timeout = static_cast<uint32_t>(json[PUBSEC_CONFIG_TIMEOUT].asInt());
                    }

                    if (json.isMember(PUBSEC_CONFIG_RETRY_COUNT) && json[PUBSEC_CONFIG_RETRY_COUNT].isInt()) {
                        newConfig.retryCount = static_cast<uint32_t>(json[PUBSEC_CONFIG_RETRY_COUNT].asInt());
                    }

                    if (json.isMember(PUBSEC_CONFIG_TRANS_METHOD) && json[PUBSEC_CONFIG_TRANS_METHOD].isString()) {
                        std::string method = json[PUBSEC_CONFIG_TRANS_METHOD].asString();
                        if (method == "https") {
                            newConfig.transMethod = PUBSEC_TRANS_HTTPS;
                        } else if (method == "ftp") {
                            newConfig.transMethod = PUBSEC_TRANS_FTP;
                        } else {
                            newConfig.transMethod = PUBSEC_TRANS_HTTP;
                        }
                    }

                    // 验证配置
                    auto validationResult = validator_->validate(newConfig);
                    if (validationResult.isError()) {
                        return validationResult;
                    }

                    // 保存配置
                    config_ = newConfig;
                    loaded_ = true;

                    return Result<bool>::success(true);

                } catch (const std::exception& e) {
                    return Result<bool>::error(PUBSEC_CLT_RESP_ERROR,
                        std::string("Failed to parse JSON configuration: ") + e.what());
                }
            }

            /**
             * @brief 获取配置
             * @return 配置对象的常量引用
             */
            const PubsecConfiguration& getConfig() const {
                PUBSEC_CHECK_PARAM(loaded_, "Configuration not loaded");
                return config_;
            }

            /**
             * @brief 检查配置是否已加载
             * @return true表示已加载
             */
            bool isLoaded() const {
                return loaded_;
            }

            /**
             * @brief 检查是否启用
             * @return true表示启用
             */
            bool isEnabled() const {
                return loaded_ && config_.enable;
            }

            /**
             * @brief 获取平台URL
             * @return 平台URL
             */
            std::string getPlatformUrl() const {
                PUBSEC_CHECK_PARAM(loaded_, "Configuration not loaded");
                return config_.getPlatformUrl();
            }

            /**
             * @brief 获取设备ID
             * @return 设备ID
             */
            const std::string& getDeviceId() const {
                PUBSEC_CHECK_PARAM(loaded_, "Configuration not loaded");
                return config_.deviceId;
            }

            /**
             * @brief 获取用户名
             * @return 用户名
             */
            const std::string& getUsername() const {
                PUBSEC_CHECK_PARAM(loaded_, "Configuration not loaded");
                return config_.username;
            }

            /**
             * @brief 获取密码
             * @return 密码
             */
            const std::string& getPassword() const {
                PUBSEC_CHECK_PARAM(loaded_, "Configuration not loaded");
                return config_.password;
            }

            /**
             * @brief 获取心跳间隔
             * @return 心跳间隔（秒）
             */
            uint32_t getHeartbeatInterval() const {
                PUBSEC_CHECK_PARAM(loaded_, "Configuration not loaded");
                return config_.heartbeatInterval;
            }

            /**
             * @brief 获取同步间隔
             * @return 同步间隔（秒）
             */
            uint32_t getSyncInterval() const {
                PUBSEC_CHECK_PARAM(loaded_, "Configuration not loaded");
                return config_.syncInterval;
            }

            /**
             * @brief 获取超时时间
             * @return 超时时间（秒）
             */
            uint32_t getTimeout() const {
                PUBSEC_CHECK_PARAM(loaded_, "Configuration not loaded");
                return config_.timeout;
            }

            /**
             * @brief 获取重试次数
             * @return 重试次数
             */
            uint32_t getRetryCount() const {
                PUBSEC_CHECK_PARAM(loaded_, "Configuration not loaded");
                return config_.retryCount;
            }

            /**
             * @brief 重置配置
             */
            void reset() {
                config_ = PubsecConfiguration{};
                loaded_ = false;
            }

            /**
             * @brief 转换为JSON
             * @return JSON对象
             */
            Json::Value toJson() const {
                PUBSEC_CHECK_PARAM(loaded_, "Configuration not loaded");

                Json::Value json;
                json[PUBSEC_CONFIG_ENABLE] = config_.enable;
                json[PUBSEC_CONFIG_USERNAME] = config_.username;
                json[PUBSEC_CONFIG_PASSWORD] = config_.password;
                json[PUBSEC_CONFIG_DEVICE_ID] = config_.deviceId;
                json[PUBSEC_CONFIG_PLATFORM_ADDRESS] = config_.platformAddress;
                json[PUBSEC_CONFIG_VIDEO_LIB_PORT] = config_.port;
                json[PUBSEC_CONFIG_HEARTBEAT_INTERVAL] = config_.heartbeatInterval;
                json[PUBSEC_CONFIG_SYNC_INTERVAL] = config_.syncInterval;
                json[PUBSEC_CONFIG_TIMEOUT] = config_.timeout;
                json[PUBSEC_CONFIG_RETRY_COUNT] = config_.retryCount;

                std::string transMethodStr;
                switch (config_.transMethod) {
                    case PUBSEC_TRANS_HTTPS:
                        transMethodStr = "https";
                        break;
                    case PUBSEC_TRANS_FTP:
                        transMethodStr = "ftp";
                        break;
                    default:
                        transMethodStr = "http";
                        break;
                }
                json[PUBSEC_CONFIG_TRANS_METHOD] = transMethodStr;

                return json;
            }
        };

    } // namespace Application
} // namespace Uface

#endif // _INCLUDE_PUBSEC_CONFIG_H_