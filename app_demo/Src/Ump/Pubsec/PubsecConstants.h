/**
 * @file     PubsecConstants.h
 * @brief    公安视图库常量定义
 * <AUTHOR> @date     2024-01-20
 * @version  1.0
 * @copyright V1.0  Copyright(C) 2024 All rights reserved.
 */

#ifndef _INCLUDE_PUBSEC_CONSTANTS_H_
#define _INCLUDE_PUBSEC_CONSTANTS_H_
#pragma once

namespace Uface
{
    namespace Application
    {
        // 字符串长度常量（使用不同的名称避免冲突）
        const int PUBSEC_BASIC_OBJECT_ID_LENGTH = 48;      // 基础对象ID长度
        const int PUBSEC_DATE_TIME_LENGTH = 20;            // 日期时间长度
        const int PUBSEC_BASIC_STRING_LENGTH = 256;        // 基础字符串长度
        const int PUBSEC_LONG_STRING_LENGTH = 1024;        // 长字符串长度
        const int PUBSEC_EXTRA_LONG_STRING_LENGTH = 4096;  // 超长字符串长度
        const int PUBSEC_IP_ADDRESS_LENGTH = 64;           // IP地址长度
        const int PUBSEC_PORT_LENGTH = 8;                  // 端口长度
        const int PUBSEC_VERSION_LENGTH = 32;              // 版本号长度
        const int PUBSEC_DEVICE_ID_LENGTH = 20;            // 设备ID长度
        const int PUBSEC_USER_NAME_LENGTH = 64;            // 用户名长度
        const int PUBSEC_PASSWORD_LENGTH = 64;             // 密码长度
        const int PUBSEC_FILE_PATH_LENGTH = 512;           // 文件路径长度
        const int PUBSEC_URL_LENGTH = 512;                 // URL长度
        const int PUBSEC_HASH_LENGTH = 64;                 // 哈希值长度
        const int PUBSEC_TitleType = 128;                  // 标题长度
        const int PUBSEC_DescriptionType = 512;            // 描述长度
        const int PUBSEC_AddressType = 256;                // 地址长度
        const int PUBSEC_SecurityLevelType = 8;            // 安全级别长度
        const int PUBSEC_PersonCodeType = 32;              // 人员编码长度
        const int PUBSEC_CardIDType = 32;                  // 卡号长度
        const int PUBSEC_AlarmEventCodeType = 16;          // 报警事件编码长度

        // 数组大小常量
        const int PUBSEC_MAX_PLATFORM_COUNT = 8;           // 最大平台数量
        const int PUBSEC_MAX_DEVICE_COUNT = 256;           // 最大设备数量
        const int PUBSEC_MAX_IMAGE_COUNT = 64;             // 最大图像数量
        const int PUBSEC_MAX_PERSONNEL_COUNT = 64;         // 最大人员数量
        const int PUBSEC_MAX_FACE_COUNT = 64;              // 最大人脸数量
        const int PUBSEC_MAX_ACCESS_RECORD_COUNT = 64;     // 最大门禁记录数量
        const int PUBSEC_MAX_SUB_IMAGE_COUNT = 16;         // 最大子图像数量

        // 网络相关常量
        const int PUBSEC_DEFAULT_HTTP_PORT = 80;           // 默认HTTP端口
        const int PUBSEC_DEFAULT_HTTPS_PORT = 443;         // 默认HTTPS端口
        const int PUBSEC_DEFAULT_TIMEOUT = 30;             // 默认超时时间（秒）
        const int PUBSEC_DEFAULT_HEARTBEAT_INTERVAL = 60;  // 默认心跳间隔（秒）
        const int PUBSEC_DEFAULT_SYNC_INTERVAL = 3600;     // 默认同步间隔（秒）
        const int PUBSEC_MAX_RETRY_COUNT = 3;              // 最大重试次数
        const int PUBSEC_RETRY_DELAY = 5;                  // 重试延迟（秒）

        // 图像相关常量
        const int PUBSEC_DEFAULT_IMAGE_WIDTH = 176;        // 默认图像宽度
        const int PUBSEC_DEFAULT_IMAGE_HEIGHT = 202;       // 默认图像高度
        const int PUBSEC_MAX_IMAGE_SIZE = 10 * 1024 * 1024; // 最大图像大小（10MB）
        const int PUBSEC_MIN_IMAGE_WIDTH = 64;             // 最小图像宽度
        const int PUBSEC_MIN_IMAGE_HEIGHT = 64;            // 最小图像高度
        const int PUBSEC_MAX_IMAGE_WIDTH = 4096;           // 最大图像宽度
        const int PUBSEC_MAX_IMAGE_HEIGHT = 4096;          // 最大图像高度

        // 默认值常量
        const char PUBSEC_DEFAULT_IMAGE_SOURCE[] = "17";   // 默认图像来源
        const char PUBSEC_DEFAULT_FILE_FORMAT[] = "Jpeg";  // 默认文件格式
        const char PUBSEC_DEFAULT_SECURITY_LEVEL[] = "5";  // 默认安全级别
        const char PUBSEC_DEFAULT_ALARM_EVENT_CODE[] = "00-01"; // 默认报警事件编码
        const char PUBSEC_DEFAULT_PERSON_CODE[] = "3424";  // 默认人员编码
        const char PUBSEC_DEFAULT_SUB_IMAGE_TYPE[] = "11"; // 默认子图像类型
        const char PUBSEC_DEFAULT_STORAGE_PATH_PREFIX[] = "http://"; // 默认存储路径前缀

        // 配置键名常量
        const char PUBSEC_CONFIG_ENABLE[] = "enable";
        const char PUBSEC_CONFIG_USERNAME[] = "username";
        const char PUBSEC_CONFIG_PASSWORD[] = "password";
        const char PUBSEC_CONFIG_DEVICE_ID[] = "deviceId";
        const char PUBSEC_CONFIG_PLATFORM_ADDRESS[] = "platformAddress";
        const char PUBSEC_CONFIG_VIDEO_LIB_PORT[] = "videoLibPort";
        const char PUBSEC_CONFIG_HEARTBEAT_INTERVAL[] = "heartbeatInterval";
        const char PUBSEC_CONFIG_SYNC_INTERVAL[] = "syncInterval";
        const char PUBSEC_CONFIG_TIMEOUT[] = "timeout";
        const char PUBSEC_CONFIG_RETRY_COUNT[] = "retryCount";
        const char PUBSEC_CONFIG_TRANS_METHOD[] = "transMethod";

        // URI路径常量
        const char PUBSEC_URI_REGISTER[] = "/VIID/System/Register";
        const char PUBSEC_URI_UNREGISTER[] = "/VIID/System/UnRegister";
        const char PUBSEC_URI_KEEPALIVE[] = "/VIID/System/Keepalive";
        const char PUBSEC_URI_TIME[] = "/VIID/System/Time";
        const char PUBSEC_URI_IMAGE_GATHER[] = "/VIID/Images";
        const char PUBSEC_URI_PERSONNEL[] = "/VIID/Persons";
        const char PUBSEC_URI_FACE[] = "/VIID/Faces";
        const char PUBSEC_URI_ACCESS_RECORD[] = "/VIID/AccessRecords";

        // HTTP头部常量
        const char PUBSEC_HTTP_HEADER_CONTENT_TYPE[] = "Content-Type: application/json";
        const char PUBSEC_HTTP_HEADER_USER_AGENT[] = "User-Agent: VIID-Client/1.0";
        const char PUBSEC_HTTP_HEADER_ACCEPT[] = "Accept: application/json";
        const char PUBSEC_HTTP_HEADER_CONNECTION[] = "Connection: keep-alive";

        // 日志级别常量
        const char PUBSEC_LOG_LEVEL_DEBUG[] = "DEBUG";
        const char PUBSEC_LOG_LEVEL_INFO[] = "INFO";
        const char PUBSEC_LOG_LEVEL_WARN[] = "WARN";
        const char PUBSEC_LOG_LEVEL_ERROR[] = "ERROR";
        const char PUBSEC_LOG_LEVEL_FATAL[] = "FATAL";

        // 版本信息常量
        const char PUBSEC_VERSION[] = "1.0.0";
        const char PUBSEC_BUILD_DATE[] = __DATE__;
        const char PUBSEC_BUILD_TIME[] = __TIME__;

    } // namespace Application
} // namespace Uface

#endif // _INCLUDE_PUBSEC_CONSTANTS_H_