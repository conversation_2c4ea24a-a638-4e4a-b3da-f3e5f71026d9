/**
 * @file     PubsecErrors.h
 * @brief    公安视图库统一错误处理机制
 * <AUTHOR> @date     2024-01-20
 * @version  1.0
 * @copyright V1.0  Copyright(C) 2024 All rights reserved.
 */

#ifndef _INCLUDE_PUBSEC_ERRORS_H_
#define _INCLUDE_PUBSEC_ERRORS_H_
#pragma once

#include <string>
#include <exception>
#include <sstream>
#include "PubsecDefine.h"

namespace Uface
{
    namespace Application
    {
        /**
         * @brief 公安视图库异常基类
         */
        class PubsecException : public std::exception {
        private:
            EPubSecCltRet errorCode_;
            std::string message_;
            std::string fullMessage_;

        public:
            /**
             * @brief 构造函数
             * @param errorCode 错误码
             * @param message 错误消息
             */
            PubsecException(EPubSecCltRet errorCode, const std::string& message)
                : errorCode_(errorCode), message_(message) {
                std::ostringstream oss;
                oss << "PubsecError[" << static_cast<int>(errorCode) << "]: " << message;
                fullMessage_ = oss.str();
            }

            /**
             * @brief 获取错误码
             * @return 错误码
             */
            EPubSecCltRet getErrorCode() const { return errorCode_; }

            /**
             * @brief 获取错误消息
             * @return 错误消息
             */
            const std::string& getMessage() const { return message_; }

            /**
             * @brief 重写what方法
             * @return 完整错误信息
             */
            const char* what() const noexcept override {
                return fullMessage_.c_str();
            }
        };

        /**
         * @brief 内存分配异常
         */
        class PubsecMemoryException : public PubsecException {
        public:
            PubsecMemoryException(const std::string& message)
                : PubsecException(PUBSEC_CLT_NO_MEMORY, "Memory allocation failed: " + message) {}
        };

        /**
         * @brief 参数无效异常
         */
        class PubsecInvalidParameterException : public PubsecException {
        public:
            PubsecInvalidParameterException(const std::string& message)
                : PubsecException(PUBSEC_CLT_PARAM_INVALID, "Invalid parameter: " + message) {}
        };

        /**
         * @brief 网络通信异常
         */
        class PubsecNetworkException : public PubsecException {
        public:
            PubsecNetworkException(const std::string& message)
                : PubsecException(PUBSEC_CLT_RESP_ERROR, "Network error: " + message) {}
        };

        /**
         * @brief JSON解析异常
         */
        class PubsecJsonException : public PubsecException {
        public:
            PubsecJsonException(const std::string& message)
                : PubsecException(PUBSEC_CLT_RESP_ERROR, "JSON parsing error: " + message) {}
        };

        /**
         * @brief 错误处理工具类
         */
        class ErrorHandler {
        public:
            /**
             * @brief 将错误码转换为字符串描述
             * @param errorCode 错误码
             * @return 错误描述
             */
            static std::string errorCodeToString(EPubSecCltRet errorCode) {
                switch (errorCode) {
                    case PUBSEC_CLT_OK:
                        return "Success";
                    case PUBSEC_CLT_ERR:
                        return "Other error";
                    case PUBSEC_CLT_INIT_AGAIN:
                        return "Library initialization failed";
                    case PUBSEC_CLT_IP_ADDRESS_ERROR:
                        return "IP address information error";
                    case PUBSEC_CLT_DEVICE_UNREGISTER:
                        return "Device not registered";
                    case PUBSEC_CLT_OUTTIME:
                        return "Connection timeout";
                    case PUBSEC_CLT_REGISTER_ERROR:
                        return "Registration failed";
                    case PUBSEC_CLT_MULTIPLE_REGISTER:
                        return "Duplicate registration";
                    case PUBSEC_CLT_POST_ERROR:
                        return "Upload failed";
                    case PUBSEC_CLT_NO_MEMORY:
                        return "Memory allocation error";
                    case PUBSEC_CLT_HEADER_ERROR:
                        return "Header information error";
                    case PUBSEC_CLT_SET_BODY_ERROR:
                        return "Set body error";
                    case PUBSEC_CLT_RESP_ERROR:
                        return "HTTP request error";
                    case PUBSEC_CLT_NO_RESPOND_STATUS:
                        return "Failed to get response status";
                    case PUBSEC_CLT_NO_RESPOND:
                        return "Failed to get response content";
                    case PUBSEC_CLT_SERVER_ERROR:
                        return "Server internal error";
                    case PUBSEC_CLT_HTTP_UNAUTHORIZED:
                        return "Authentication failed";
                    case PUBSEC_CLT_PARAM_INVALID:
                        return "Invalid parameter";
                    case PUBSEC_CLT_OPENFILE_FAILED:
                        return "Failed to open file";
                    default:
                        return "Unknown error";
                }
            }

            /**
             * @brief 记录错误日志
             * @param errorCode 错误码
             * @param message 错误消息
             * @param function 函数名
             * @param line 行号
             */
            static void logError(EPubSecCltRet errorCode, const std::string& message,
                               const char* function = nullptr, int line = 0) {
                std::ostringstream oss;
                oss << "[ERROR] " << errorCodeToString(errorCode) << ": " << message;
                if (function) {
                    oss << " (in " << function;
                    if (line > 0) {
                        oss << ":" << line;
                    }
                    oss << ")";
                }

                // 这里可以集成到现有的日志系统
                // 暂时使用printf输出
                printf("%s\n", oss.str().c_str());
            }

            /**
             * @brief 检查参数有效性
             * @param condition 条件表达式
             * @param message 错误消息
             * @throws PubsecInvalidParameterException 参数无效时抛出
             */
            static void checkParameter(bool condition, const std::string& message) {
                if (!condition) {
                    throw PubsecInvalidParameterException(message);
                }
            }

            /**
             * @brief 检查内存分配结果
             * @param ptr 指针
             * @param message 错误消息
             * @throws PubsecMemoryException 内存分配失败时抛出
             */
            template<typename T>
            static void checkMemoryAllocation(T* ptr, const std::string& message) {
                if (!ptr) {
                    throw PubsecMemoryException(message);
                }
            }
        };

        /**
         * @brief 错误处理宏定义
         */
        #define PUBSEC_CHECK_PARAM(condition, message) \
            ErrorHandler::checkParameter(condition, message)

        #define PUBSEC_CHECK_MEMORY(ptr, message) \
            ErrorHandler::checkMemoryAllocation(ptr, message)

        #define PUBSEC_LOG_ERROR(errorCode, message) \
            ErrorHandler::logError(errorCode, message, __FUNCTION__, __LINE__)

    } // namespace Application
} // namespace Uface

#endif // _INCLUDE_PUBSEC_ERRORS_H_