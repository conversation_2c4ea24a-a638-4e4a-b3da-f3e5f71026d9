/**
 * @file     PubsecRAII.h
 * @brief    公安视图库RAII资源管理封装
 * <AUTHOR> @date     2024-01-20
 * @version  1.0
 * @copyright V1.0  Copyright(C) 2024 All rights reserved.
 */

#ifndef _INCLUDE_PUBSEC_RAII_H_
#define _INCLUDE_PUBSEC_RAII_H_
#pragma once

#include <memory>
#include <vector>
#include <functional>
#include <stdexcept>
#include <cstring>
#include <cstdio>
#include <string>
#include <algorithm>
#include <atomic>
#include <thread>
#include <chrono>
#include "PubsecDefine.h"
#include "PubsecErrors.h"

namespace Uface
{
    namespace Application
    {
        /**
         * @brief 安全字符串工具类
         * 提供安全的字符串拷贝操作，避免缓冲区溢出
         */
        class SafeStringUtils {
        public:
            /**
             * @brief 安全拷贝字符串到固定大小数组
             * @tparam N 目标数组大小
             * @param dest 目标数组
             * @param src 源字符串
             */
            template<size_t N>
            static void safeCopy(char (&dest)[N], const std::string& src) {
                size_t copyLen = std::min(src.length(), N - 1);
                std::memcpy(dest, src.c_str(), copyLen);
                dest[copyLen] = '\0';
            }

            /**
             * @brief 安全拷贝C字符串到固定大小数组
             * @tparam N 目标数组大小
             * @param dest 目标数组
             * @param src 源C字符串
             */
            template<size_t N>
            static void safeCopy(char (&dest)[N], const char* src) {
                if (src) {
                    size_t srcLen = std::strlen(src);
                    size_t copyLen = std::min(srcLen, N - 1);
                    std::memcpy(dest, src, copyLen);
                    dest[copyLen] = '\0';
                } else {
                    dest[0] = '\0';
                }
            }

            /**
             * @brief 格式化字符串到固定大小数组
             * @tparam N 目标数组大小
             * @param dest 目标数组
             * @param format 格式字符串
             * @param args 格式参数
             */
            template<size_t N, typename... Args>
            static void safeFormat(char (&dest)[N], const char* format, Args... args) {
                int result = std::snprintf(dest, N, format, args...);
                if (result < 0 || static_cast<size_t>(result) >= N) {
                    dest[N - 1] = '\0'; // 确保字符串终止
                }
            }
        };

        /**
         * @brief 子图像列表RAII管理器
         * 自动管理TPubSecSubImageList数组的内存分配和释放
         */
        class SubImageListManager {
        private:
            std::unique_ptr<TPubSecSubImageList[]> images_;
            size_t count_;

        public:
            /**
             * @brief 构造函数
             * @param count 子图像数量
             */
            explicit SubImageListManager(size_t count)
                : images_(new TPubSecSubImageList[count], std::default_delete<TPubSecSubImageList[]>())
                , count_(count) {
                // 初始化所有子图像
                for (size_t i = 0; i < count_; ++i) {
                    std::memset(&images_[i], 0, sizeof(TPubSecSubImageList));
                }
            }

            /**
             * @brief 获取指定索引的子图像
             * @param index 索引
             * @return 子图像引用
             */
            TPubSecSubImageList& operator[](size_t index) {
                PUBSEC_CHECK_PARAM(index < count_, "SubImageListManager index out of range");
                return images_[index];
            }

            /**
             * @brief 获取原始指针（用于C接口）
             * @return 原始指针
             */
            TPubSecSubImageList* get() { return images_.get(); }

            /**
             * @brief 获取数量
             * @return 子图像数量
             */
            size_t size() const { return count_; }
        };

        /**
         * @brief 错误结果包装器
         * 提供统一的错误处理机制
         */
        template<typename T>
        class Result {
        private:
            bool success_;
            T value_;
            EPubSecCltRet errorCode_;
            std::string errorMessage_;

        public:
            // 成功构造函数
            explicit Result(T&& value)
                : success_(true), value_(std::move(value)), errorCode_(PUBSEC_CLT_OK) {}

            // 失败构造函数
            Result(EPubSecCltRet errorCode, const std::string& errorMessage)
                : success_(false), value_{}, errorCode_(errorCode), errorMessage_(errorMessage) {}

            bool isSuccess() const { return success_; }
            bool isError() const { return !success_; }

            const T& getValue() const {
                if (!success_) {
                    throw std::runtime_error("Attempting to get value from failed result: " + errorMessage_);
                }
                return value_;
            }

            T& getValue() {
                if (!success_) {
                    throw std::runtime_error("Attempting to get value from failed result: " + errorMessage_);
                }
                return value_;
            }

            EPubSecCltRet getErrorCode() const { return errorCode_; }
            const std::string& getErrorMessage() const { return errorMessage_; }

            // 静态工厂方法
            static Result<T> success(T&& value) {
                return Result<T>(std::move(value));
            }

            static Result<T> error(EPubSecCltRet errorCode, const std::string& message) {
                return Result<T>(errorCode, message);
            }
        };

        /**
         * @brief 图像采集对象RAII管理器
         * 自动管理TPubSecObjList相关的所有内存分配和释放
         */
        class ImageGatherManager {
        private:
            std::unique_ptr<TPubSecImageObjInfo[]> imageList_;
            std::unique_ptr<TPubSecPersonnelObjInfo[]> personnelList_;
            std::unique_ptr<TPubSecFaceObjInfo[]> faceList_;
            std::unique_ptr<TPubSecAccessRecord[]> accessRecords_;
            std::vector<std::unique_ptr<SubImageListManager>> subImageManagers_;

            size_t imageCount_;
            size_t personnelCount_;
            size_t faceCount_;
            size_t accessRecordCount_;

        public:
            /**
             * @brief 构造函数
             * @param imageCount 图像数量
             * @param personnelCount 人员数量
             * @param faceCount 人脸数量
             * @param accessRecordCount 门禁记录数量
             */
            ImageGatherManager(size_t imageCount, size_t personnelCount,
                             size_t faceCount, size_t accessRecordCount)
                : imageList_(imageCount > 0 ?
                    std::unique_ptr<TPubSecImageObjInfo[]>(new TPubSecImageObjInfo[imageCount]) : nullptr)
                , personnelList_(personnelCount > 0 ?
                    std::unique_ptr<TPubSecPersonnelObjInfo[]>(new TPubSecPersonnelObjInfo[personnelCount]) : nullptr)
                , faceList_(faceCount > 0 ?
                    std::unique_ptr<TPubSecFaceObjInfo[]>(new TPubSecFaceObjInfo[faceCount]) : nullptr)
                , accessRecords_(accessRecordCount > 0 ?
                    std::unique_ptr<TPubSecAccessRecord[]>(new TPubSecAccessRecord[accessRecordCount]) : nullptr)
                , imageCount_(imageCount)
                , personnelCount_(personnelCount)
                , faceCount_(faceCount)
                , accessRecordCount_(accessRecordCount) {

                // 初始化所有对象
                if (imageList_) {
                    for (size_t i = 0; i < imageCount_; ++i) {
                        std::memset(&imageList_[i], 0, sizeof(TPubSecImageObjInfo));
                    }
                }

                if (personnelList_) {
                    for (size_t i = 0; i < personnelCount_; ++i) {
                        std::memset(&personnelList_[i], 0, sizeof(TPubSecPersonnelObjInfo));
                    }
                }

                if (faceList_) {
                    for (size_t i = 0; i < faceCount_; ++i) {
                        std::memset(&faceList_[i], 0, sizeof(TPubSecFaceObjInfo));
                    }
                }

                if (accessRecords_) {
                    for (size_t i = 0; i < accessRecordCount_; ++i) {
                        std::memset(&accessRecords_[i], 0, sizeof(TPubSecAccessRecord));
                    }
                }
            }

            /**
             * @brief 为人员对象分配子图像
             * @param personnelIndex 人员索引
             * @param subImageCount 子图像数量
             * @return 子图像管理器指针
             */
            SubImageListManager* allocateSubImages(size_t personnelIndex, size_t subImageCount) {
                if (personnelIndex >= personnelCount_ || !personnelList_) {
                    return nullptr;
                }

                auto manager = std::unique_ptr<SubImageListManager>(new SubImageListManager(subImageCount));
                SubImageListManager* result = manager.get();

                // 设置人员对象的子图像指针
                personnelList_[personnelIndex].ptPersonSubImage = manager->get();
                personnelList_[personnelIndex].nSubImageNum = static_cast<unsigned short>(subImageCount);

                // 保存管理器
                subImageManagers_.push_back(std::move(manager));

                return result;
            }

            /**
             * @brief 转换为TPubSecObjList结构
             * @return TPubSecObjList对象
             */
            TPubSecObjList toObjList() {
                TPubSecObjList result{};
                std::memset(&result, 0, sizeof(result));

                // 设置数量
                result.achObjectDataMSG[1][0] = static_cast<unsigned char>(imageCount_);
                result.achObjectDataMSG[1][1] = static_cast<unsigned char>(personnelCount_);
                result.achObjectDataMSG[1][2] = static_cast<unsigned char>(faceCount_);

                // 设置指针
                result.ptPubSecImageList = imageList_.get();
                result.ptPersonnelObjlist = personnelList_.get();
                result.ptFaceObjlist = faceList_.get();
                result.ptWisComAcceRec = accessRecords_.get();

                return result;
            }

            // 访问器方法
            TPubSecImageObjInfo* getImageList() { return imageList_.get(); }
            TPubSecPersonnelObjInfo* getPersonnelList() { return personnelList_.get(); }
            TPubSecFaceObjInfo* getFaceList() { return faceList_.get(); }
            TPubSecAccessRecord* getAccessRecords() { return accessRecords_.get(); }

            TPubSecImageObjInfo& getImage(size_t index) {
                PUBSEC_CHECK_PARAM(index < imageCount_ && imageList_, "Image index out of range or null pointer");
                return imageList_[index];
            }

            TPubSecPersonnelObjInfo& getPersonnel(size_t index) {
                PUBSEC_CHECK_PARAM(index < personnelCount_ && personnelList_, "Personnel index out of range or null pointer");
                return personnelList_[index];
            }

            TPubSecFaceObjInfo& getFace(size_t index) {
                PUBSEC_CHECK_PARAM(index < faceCount_ && faceList_, "Face index out of range or null pointer");
                return faceList_[index];
            }

            TPubSecAccessRecord& getAccessRecord(size_t index) {
                PUBSEC_CHECK_PARAM(index < accessRecordCount_ && accessRecords_, "Access record index out of range or null pointer");
                return accessRecords_[index];
            }

            // 获取数量
            size_t getImageCount() const { return imageCount_; }
            size_t getPersonnelCount() const { return personnelCount_; }
            size_t getFaceCount() const { return faceCount_; }
            size_t getAccessRecordCount() const { return accessRecordCount_; }
        };

        /**
         * @brief 心跳管理器
         * 简化的心跳线程实现，提高可维护性
         */
        class HeartbeatManager {
        private:
            std::atomic<bool> running_;
            std::thread worker_;
            std::chrono::seconds interval_;
            std::function<bool()> heartbeatCallback_;

        public:
            /**
             * @brief 构造函数
             */
            HeartbeatManager() : running_(false), interval_(60) {}

            /**
             * @brief 析构函数
             */
            ~HeartbeatManager() {
                stop();
            }

            /**
             * @brief 启动心跳线程
             * @param interval 心跳间隔（秒）
             * @param callback 心跳回调函数，返回true表示成功
             */
            void start(std::chrono::seconds interval, std::function<bool()> callback) {
                if (running_.load()) {
                    return; // 已经在运行
                }

                interval_ = interval;
                heartbeatCallback_ = callback;
                running_ = true;

                worker_ = std::thread([this]() {
                    while (running_.load()) {
                        try {
                            if (heartbeatCallback_ && heartbeatCallback_()) {
                                // 心跳成功，等待正常间隔
                                std::this_thread::sleep_for(interval_);
                            } else {
                                // 心跳失败，等待较短时间后重试
                                std::this_thread::sleep_for(std::chrono::seconds(5));
                            }
                        } catch (const std::exception& e) {
                            // 异常处理，等待较短时间后重试
                            std::this_thread::sleep_for(std::chrono::seconds(5));
                        }
                    }
                });
            }

            /**
             * @brief 停止心跳线程
             */
            void stop() {
                running_ = false;
                if (worker_.joinable()) {
                    worker_.join();
                }
            }

            /**
             * @brief 检查是否正在运行
             * @return true表示正在运行
             */
            bool isRunning() const {
                return running_.load();
            }

            /**
             * @brief 设置心跳间隔
             * @param interval 新的心跳间隔
             */
            void setInterval(std::chrono::seconds interval) {
                interval_ = interval;
            }
        };

    } // namespace Application
} // namespace Uface

#endif // _INCLUDE_PUBSEC_RAII_H_