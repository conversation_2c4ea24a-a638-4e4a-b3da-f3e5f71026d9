/**
 * @file     PubsecTypes.h
 * @brief    公安视图库基础数据类型定义
 * <AUTHOR> @date     2024-01-20
 * @version  1.0
 * @copyright V1.0  Copyright(C) 2024 All rights reserved.
 */

#ifndef _INCLUDE_PUBSEC_TYPES_H_
#define _INCLUDE_PUBSEC_TYPES_H_
#pragma once

#include <cstdint>

namespace Uface
{
    namespace Application
    {
        // 基础类型定义
        typedef unsigned char u8;
        typedef unsigned short u16;
        typedef unsigned int u32;
        typedef unsigned long long u64;

        typedef signed char s8;
        typedef signed short s16;
        typedef signed int s32;
        typedef signed long long s64;

        // 布尔值结构体
        template<typename T>
        struct OptionalValue {
            bool bHas;
            T value;

            OptionalValue() : bHas(false), value{} {}
            OptionalValue(const T& val) : bHas(true), value(val) {}

            void set(const T& val) {
                bHas = true;
                value = val;
            }

            void clear() {
                bHas = false;
                value = T{};
            }

            bool hasValue() const { return bHas; }
            const T& getValue() const { return value; }
            T& getValue() { return value; }
        };

        // 常用的可选值类型
        typedef OptionalValue<u32> OptionalU32;
        typedef OptionalValue<s32> OptionalS32;
        typedef OptionalValue<u16> OptionalU16;
        typedef OptionalValue<s16> OptionalS16;
        typedef OptionalValue<u8> OptionalU8;
        typedef OptionalValue<s8> OptionalS8;
        typedef OptionalValue<float> OptionalFloat;
        typedef OptionalValue<double> OptionalDouble;

        // 公安视图库返回状态枚举
        typedef enum
        {
            PUBSEC_CLT_OK = 50,                       //<成功>
            PUBSEC_CLT_ERR = 51,                      //<其他错误>
            PUBSEC_CLT_INIT_AGAIN = 52,               //<库初始化失败>
            PUBSEC_CLT_IP_ADDRESS_ERROR = 53,         //<IP地址信息错误>
            PUBSEC_CLT_DEVICE_UNREGISTER = 54,        //<设备未注册>
            PUBSEC_CLT_OUTTIME = 55,                  //<连接超时>
            PUBSEC_CLT_REGISTER_ERROR = 56,           //<注册失败>
            PUBSEC_CLT_MULTIPLE_REGISTER = 57,        //<重复注册>
            PUBSEC_CLT_POST_ERROR = 58,               //<上传失败>
            PUBSEC_CLT_NO_MEMORY = 59,                //<内存不足>
            PUBSEC_CLT_HEADER_ERROR = 60,             //<头部信息错误>
            PUBSEC_CLT_SET_BODY_ERROR = 61,           //<填入主体错误>
            PUBSEC_CLT_RESP_ERROR = 62,               //<HTTP请求错误>
            PUBSEC_CLT_NO_RESPOND_STATUS = 63,        //<获得响应状态失败>
            PUBSEC_CLT_NO_RESPOND = 64,               //<获得响应内容失败>
            PUBSEC_CLT_SERVER_ERROR = 65,             //<服务器错误>
            PUBSEC_CLT_SEARCH_HANDLEPOOL_ERR = 66,    // 搜索线程池失败
            PUBSEC_CLT_SEARCH_DESTROY_ERR = 67,       // 删除失败
            PUBSEC_CLT_HTTP_MULTIPLE_CHOICES = 68,    //<表示要完成请求需要进一步操作,重定向300>
            PUBSEC_CLT_HTTP_BAD_REQUEST = 69,         //<错误请求,服务器不理解请求的语法400>
            PUBSEC_CLT_HTTP_UNAUTHORIZED = 70,        //<未鉴权401>
            PUBSEC_CLT_HTTP_NOT_FOUND = 71,           //<未找到404>
            PUBSEC_CLT_HTTP_REQUESTTIMEOUT = 72,      //<请求超时 408>
            PUBSEC_CLT_HTTP_SERVERERR = 73,           //<服务器错误 大于500>
            PUBSEC_STATUS_CURL_CREATE_HEADR_ERR = 74, // Curl头部添加信息失败
            PUBSEC_STATUS_CURL_GLOBAL_INIT_ERR = 75,  // Curl初始化失败
            PUBSEC_STATUS_CURL_PERFORM_ERR = 76,      // Curl发送失败
            PUBSEC_STATUS_CURL_GET_RESPINF_ERR = 77,  // Curl获得响应失败
            PUBSEC_STATUS_CURL_NO_CERTIFICATE = 78,   // 证书不存在
            PUBSEC_CLT_OPENFILE_FAILED = 79,          // <fopen打开文件失败>
            PUBSEC_CLT_PARAM_INVALID = 80,            // 入参错误
        } EPubSecCltRet;

        // 视图库传输方法枚举
        typedef enum
        {
            PUBSEC_TRANS_HTTP = 0,      // HTTP传输
            PUBSEC_TRANS_HTTPS = 1,     // HTTPS传输
            PUBSEC_TRANS_FTP = 2,       // FTP传输
        } EPubSecTransMethod;

        // 信息分类枚举
        typedef enum
        {
            PUBSEC_INTELLI_MAN_TYPE_AUTO = 0,    // 自动
            PUBSEC_INTELLI_MAN_TYPE_MANUAL = 1,  // 手动
        } EPubSecIntelliManType;

        // 性别枚举
        typedef enum
        {
            PUBSEC_GENDER_UNKNOWN = 0,   // 未知
            PUBSEC_GENDER_MALE = 1,      // 男性
            PUBSEC_GENDER_FEMALE = 2,    // 女性
        } EPubSecGender;

        // 年龄段枚举
        typedef enum
        {
            PUBSEC_AGE_UNKNOWN = 0,      // 未知
            PUBSEC_AGE_CHILD = 1,        // 儿童
            PUBSEC_AGE_YOUTH = 2,        // 青年
            PUBSEC_AGE_MIDDLE = 3,       // 中年
            PUBSEC_AGE_OLD = 4,          // 老年
        } EPubSecAgeGroup;

    } // namespace Application
} // namespace Uface

#endif // _INCLUDE_PUBSEC_TYPES_H_