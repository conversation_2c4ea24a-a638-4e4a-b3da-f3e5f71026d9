/************************************************
 * Copyright(c) 2020 ump
 *
 * Project:    Pubsec
 * FileName:   Pubsec.cpp
 * Author:
 * Email:
 * Version:    V1.0.0
 * Date:       2023-07-20
 * Description: 公安视图库初始化类实现
 * Others:
 *************************************************/

#include <functional>
#include <algorithm>
#include <cstring>
#include <cstdio>
#include <arpa/inet.h>
#include <fstream>

#include "UBase/Logger/Define.h"
#include "Config/AppConfig.h"
#include "Utils/String.h"
#include "AppEvent/EventManager.h"
#include "UBase/Time/Time.h"
#include "System/System.h"
#include "System/AlarmCtrl.h"
#include "AlgReactor/RecordService.h"
#include "Device/AlgReactor/IdentifyCommon.h"
#include "Traffic/Data/DataAction.h"
#include "Object/Factory.h"
#include "Private/PrivateAccessCtrl.h"
#include "Service/NetworkCenter.h"
#include "System/SwitchCtrl.h"
#include "Http/HttpClient.h"
#include "curl/curl.h"
#include "Json/Value.h"
#include "Json/Writer.h"
#include "Json/Reader.h"
#include "Common.h"
#include "OfflineApi/Protocol/Common.h"
#include "AlgReactor/AlgManager.h"
#include "Dao/Define.h"
#include "Client/PubsecClient.h"
#include "UmpPubsec.h"
#include "Web/PlatformKey.h"
#include "AlgReactor/AlgSnapshot.h"
#include "AlgReactor/RecordService.h"
#include "AlgReactor/AlgRegister.h"
#include "UBase/File/File.h"
#include "Resource/ResourceManager.h"

namespace Uface
{
    namespace Application
    {

        // 初始化静态成员变量
        // Pubsec *Pubsec::m_instance = nullptr;
        // std::mutex Pubsec::m_instanceMutex;
        std::map<std::string, std::string> Pubsec::m_configMap;

        // 构造函数
        Pubsec::Pubsec() : m_initialized(false), m_registered(false),
                            m_registerThreadRunning(false), m_cfgChange(false)
        {
            // 初始化配置信息
            m_configMap["version"] = "1.0.0";
            m_configMap["log_level"] = "1"; // 默认日志级别

            // 注册命令处理函数, 打印当前的pubsec状态
            UmpCore::instance().registerCommand("pubsec", std::bind(&Pubsec::pubsecCommand, this, std::placeholders::_1));

            infof("Pubsec instance created\n");
        }

        // 析构函数
        Pubsec::~Pubsec()
        {
            // 确保在析构时进行反初始化
            if (m_initialized)
            {
                deinitial();
            }
            
            // 确保所有线程已停止
            stopAllThreads();

            infof("Pubsec instance destroyed\n");
        }

        // 停止所有线程
        void Pubsec::stopAllThreads()
        {
            stopRegisterThread();
        }

        // 获取单例实例 - 使用C++11局部静态变量
        Pubsec* Pubsec::instance()
        {
            static Pubsec instance;
            return &instance;
        }

        // 初始化视图库
        bool Pubsec::initial()
        {
            // 如果已经初始化，直接返回true
            if (m_initialized)
            {
                errorf("Pubsec already initialized\n");
                return true;
            }

            mDataActionPtr = Object::IFactory::instance<Data::IDataAction>("local.dataAction");

            //平台配置监听
            bool configResult = AppConfig::instance()->attach("customPubsec",AppConfig::Proc(&Pubsec::onConfig,this));
            debugf("Platform config monitoring registration %s\n", configResult ? "succeeded" : "failed");

            //报警回调
            bool alarmResult = AppEvent::IEventManager::instance()->attach(AppEvent::EventType::system, AppEvent::IEventManager::Proc(&Pubsec::onAlarm, this));
            debugf("Alarm callback registration %s\n", alarmResult ? "succeeded" : "failed");

            //识别回调
            bool recordResult = attachRecordCallback();
            debugf("Recognition callback registration %s\n", recordResult ? "succeeded" : "failed");

            //数据回调
            bool dataResult = mDataActionPtr->attach(Data::IDataAction::Proc(&Pubsec::onData,this));
            debugf("Data callback registration %s\n", dataResult ? "succeeded" : "failed");

            debugf("Initializing Pubsec...\n");

            // 调用底层视图库初始化函数
            bool result = initialStack();

            if (result)
            {
                m_initialized = true;
                infof("Pubsec initialized successfully\n");
            }
            else
            {
                errorf("Failed to initialize Pubsec\n"); // 错误级别日志
            }

            return result;
        }

        // 反初始化视图库
        void Pubsec::deinitial()
        {
            if (!m_initialized)
            {
                errorf("Pubsec not initialized, nothing to deinitial\n");
                return;
            }

            infof("Deinitializing Pubsec...\n");
            
            // 停止所有线程
            stopAllThreads();

            // 注销识别回调
            detachRecordCallback();

            m_initialized = false;
            infof("Pubsec deinitialized successfully\n");
        }

        // 获取初始化状态
        bool Pubsec::isInitialized() const
        {
            return m_initialized;
        }

        // 内部方法：初始化视图库栈
        bool Pubsec::initialStack()
        {
            Json::Value pubsecCfg = AppConfig::instance()->getAppConfig("customPubsec");
            if(pubsecCfg.empty() ) {    
                errorf("get config error ! \n");
                return false;
            }

            // 使用新封装的函数解析JSON参数
            jsonToParam(pubsecCfg, &m_pubsecCommonInfo[0]);
            // 创建并启动视图库注册线程
            createRegisterThread();

            infof("PubSecStack initialized successfully\n");
            return true;
        }

        // 获取视图库注册状态
        bool Pubsec::getRegisterStatus() const
        {
            return m_registered;
        }

        // 注册识别回调方法
        bool Pubsec::attachRecordCallback()
        {
            debugf("Registering record callback\n");
            return AlgReactor::IRecordService::instance()->attachRecordProc(
                RecordObject::platformCOI,
                AlgReactor::IRecordService::RecordProc(&Pubsec::onRecord, this)
            );
        }

        // 注销识别回调方法
        bool Pubsec::detachRecordCallback()
        {
            debugf("Unregistering record callback\n");
            return AlgReactor::IRecordService::instance()->detachRecordProc(
                RecordObject::platformCOI,
                AlgReactor::IRecordService::RecordProc(&Pubsec::onRecord, this)
            );
        }

        // 创建并启动视图库注册线程
        void Pubsec::createRegisterThread()
        {
            // 确保旧线程已停止
            stopRegisterThread();

            // 在锁保护下设置线程运行标志，防止竞态条件
            {
                std::lock_guard<std::mutex> lock(m_registerMutex);
                m_registerThreadRunning = true;
            }

            // 创建视图库注册线程
            m_registerThread = std::thread([this]()
            {
                infof("PubSecStackRegisterThread started\n");

                EPubSecCltRet ret = PUBSEC_CLT_OK;
                PubsecClient* pubsecClient = PubsecClient::instance();
                
                while(m_registerThreadRunning)
                {
                    try
                    {
                        {
                            std::unique_lock<std::mutex> lock(m_registerMutex);
                            if (m_registerCV.wait_for(lock, std::chrono::seconds(10), [this]{
                                return m_cfgChange || !m_registerThreadRunning;
                            }))
                            {
                                // 配置变化，更新配置
                                m_cfgChange = false;

                                if (m_registered)
                                {
                                    // 注销
                                    ret = pubsecClient->postUnRegister(0);
                                    if (ret!= PUBSEC_CLT_OK)
                                    {
                                        debugf("pubsec unregister failed, ret = %d\n", ret);
                                    }
                                    m_registered = false;
                                }
                            }
                        }
                        // 如果未注册，尝试注册
                        if (!m_registered && m_pubsecCommonInfo[0].bEnable)
                        {
                            debugf("attempting to register pubsec\n");
                            ret = pubsecClient->postRegister(0, &m_pubsecCommonInfo[0]);
                            if (ret != PUBSEC_CLT_OK)
                            {
                                debugf("pubsec register failed, ret = %d, will retry in 10 seconds\n", ret);
                                continue;
                            }

                            // 注册成功
                            m_registered = true;
                            infof("Pubsec registered successfully\n");
                        }
                    }
                    catch (const std::exception& e)
                    {
                        errorf("Exception in register thread: %s\n", e.what());
                        // 发生异常时等待一段时间再继续，避免快速循环
                        std::this_thread::sleep_for(std::chrono::seconds(5));
                    }
                    catch (...)
                    {
                        errorf("Unknown exception in register thread\n");
                        // 发生未知异常时等待一段时间再继续
                        std::this_thread::sleep_for(std::chrono::seconds(5));
                    }
                }
                
                // 线程结束前确保注销
                if (m_registered) {
                    debugf("unregistering before thread exit\n");
                    pubsecClient->postUnRegister(0);
                    m_registered = false;
                }
                
                infof("PubSecStackRegisterThread finished\n");
            });
        }
        
        // 停止视图库注册线程
        void Pubsec::stopRegisterThread()
        {
            // 关键修复：在锁保护下设置停止标志和通知，避免死锁
            { // 临时作用域，确保锁在notify_all()后立即释放
                std::lock_guard<std::mutex> lock(m_registerMutex);
                m_registerThreadRunning = false;
                m_registerCV.notify_all();
            } // m_registerMutex在这里被释放！避免死锁

            // 现在互斥锁已经释放，可以安全地等待线程完成
            if (m_registerThread.joinable())
            {
                m_registerThread.join();
                infof("Register thread joined successfully\n");
            }
        }

        void Pubsec::postimagePerson()
        {
            TPubSecPersonnelObjInfo tPersonInfo;
            memset(&tPersonInfo, 0, sizeof(tPersonInfo));
            // 必选字段(R)
            strncpy(tPersonInfo.achPersonID, "person_test", sizeof(tPersonInfo.achPersonID) - 1);                 // R 人员标识
            tPersonInfo.achPersonID[sizeof(tPersonInfo.achPersonID) - 1] = '\0';
            tPersonInfo.nInfoKind = PUBSEC_INTELLI_MAN_TYPE_MANUAL;           // R 信息分类(自动采集)
            strncpy(tPersonInfo.achSourceID, "person_test", sizeof(tPersonInfo.achSourceID) - 1);                 // R 来源图像信息标识
            tPersonInfo.achSourceID[sizeof(tPersonInfo.achSourceID) - 1] = '\0';
            
            // 自动采集时必选字段(R/O)
            // tPersonInfo.tLeftTopX = 100;                                // R/O 左上角X坐标
            // tPersonInfo.tLeftTopY = 100;                                // R/O 左上角Y坐标
            // tPersonInfo.tRightBtmX = 200;                              // R/O 右下角X坐标
            // tPersonInfo.tRightBtmY = 200;                              // R/O 右下角Y坐标
            
            // 人工采集时必选字段(R/O)
            strncpy(tPersonInfo.achLocationMarkTime, "20230720102030", sizeof(tPersonInfo.achLocationMarkTime) - 1);  // R/O 位置标记时间
            tPersonInfo.achLocationMarkTime[sizeof(tPersonInfo.achLocationMarkTime) - 1] = '\0';
            strncpy(tPersonInfo.achPersonAppearTime, "20230720102030", sizeof(tPersonInfo.achPersonAppearTime) - 1);  // R/O 人员出现时间
            tPersonInfo.achPersonAppearTime[sizeof(tPersonInfo.achPersonAppearTime) - 1] = '\0';
            strncpy(tPersonInfo.achPersonDisAppearTime, "20230720102030", sizeof(tPersonInfo.achPersonDisAppearTime) - 1);// R/O 人员消失时间
            tPersonInfo.achPersonDisAppearTime[sizeof(tPersonInfo.achPersonDisAppearTime) - 1] = '\0';
            
            // 其他必选字段(R/O)
            // tPersonInfo.tIsDriver = 0;                                  // R/O 是否驾驶员
            // tPersonInfo.tIsForeigner = 0;                              // R/O 是否涉外人员
            // tPersonInfo.tIsSuspectedTerrorist = 0;                     // R/O 是否涉恐人员
            // tPersonInfo.tIsCriminalInvolved = 0;                       // R/O 是否涉案人员
            // tPersonInfo.tIsDetainees = 0;                              // R/O 是否在押人员
            // tPersonInfo.tIsSuspiciousPerson = 0;                       // R/O 是否可疑人
            
            // 可选字段示例(O)
            strncpy(tPersonInfo.achName, "test.jpg", sizeof(tPersonInfo.achName) - 1);
            tPersonInfo.achName[sizeof(tPersonInfo.achName) - 1] = '\0';
            strncpy(tPersonInfo.achIDNumber, "11010119900307001X", sizeof(tPersonInfo.achIDNumber) - 1);
            tPersonInfo.achIDNumber[sizeof(tPersonInfo.achIDNumber) - 1] = '\0'; 

            tPersonInfo.nSubImageNum = 1;

            /*
                s32_param tEventSort;                                   // R/O  自动分析事件类型,设备采集必选，不枚举了，赋值时查看《公安视频图像信息应用系统 第3部分：数据库技术要求.pdf》文档中的B.3.51 视频图像分析处理事件类型（EventType）定义
                char achType[PUBSEC_ImageType];                           // R	图像类型
                char *pSubImageBase64BinaryData;                          // R    子图像Base64数据
                char achFilePath[256];                                    // R 科达内部，文件绝对路径
    */
            TPubSecSubImageList tImgObj = {0};
            tPersonInfo.ptPersonSubImage = &tImgObj;

            tImgObj.tEventSort.bHas = true;
            tImgObj.tEventSort.value = 0;

            tImgObj.nWidth=80;
            tImgObj.nHeight=80;

            // achImageID
            strncpy(tImgObj.achImageID, "1234567890", sizeof(tImgObj.achImageID) - 1);
            tImgObj.achImageID[sizeof(tImgObj.achImageID) - 1] = '\0';

            // achDeviceID
            strncpy(tImgObj.achDeviceID, "30000000000000000303", sizeof(tImgObj.achDeviceID) - 1);
            tImgObj.achDeviceID[sizeof(tImgObj.achDeviceID) - 1] = '\0';

            // achStoragePath
            strncpy(tImgObj.achStoragePath, "/application/pubsec/test.jpg", sizeof(tImgObj.achStoragePath) - 1);
            tImgObj.achStoragePath[sizeof(tImgObj.achStoragePath) - 1] = '\0';

            // achShotTime
            time_t now = time(0);
            struct tm *ltm = localtime(&now);
            char timeStr[32];
            strftime(timeStr, sizeof(timeStr), "%Y%m%d%H%M%S", ltm);
            // 确保时间字符串不会超出缓冲区大小
            strncpy(tImgObj.achShotTime, timeStr, PUBSEC_dateTime - 1);
            tImgObj.achShotTime[PUBSEC_dateTime - 1] = '\0';

            /*
                AIS_PUBSEC_INTELLI_SUBIMG_FACE = 1,          //人脸图 11
                AIS_PUBSEC_INTELLI_SUBIMG_PERSON,            //人员图 10
                AIS_PUBSEC_INTELLI_SUBIMG_CAR,               //车辆大图01
                AIS_PUBSEC_INTELLI_SUBIMG_THUM,              //缩略图子图 99 
                AIS_PUBSEC_INTELLI_SUBIMG_GENE,              //一般图片 100
                AIS_PUBSEC_INTELLI_SUBIMG_SCEN,              //场景图 14
                AIS_PUBSEC_INTELLI_SUBIMG_THIN,              //物品图 13
                AIS_PUBSEC_INTELLI_SUBIMG_NONM,              //非机动车图 12
                AIS_PUBSEC_INTELLI_SUBIMG_VEHI,              //车辆特写图 09
                AIS_PUBSEC_INTELLI_SUBIMG_CARS,              //过车合成图 08
                AIS_PUBSEC_INTELLI_SUBIMG_VIOS,              //违章合成图 07
                AIS_PUBSEC_INTELLI_SUBIMG_LOGO,              //车标 06
                AIS_PUBSEC_INTELLI_SUBIMG_CFACIALFEA,        //副驾驶面部特征图 05
                AIS_PUBSEC_INTELLI_SUBIMG_MFACIALFEA,        //驾驶员面部特征图 04
                AIS_PUBSEC_INTELLI_SUBIMG_PLATBIN,           //车牌二值化图 03 
                AIS_PUBSEC_INTELLI_SUBIMG_PLATCOLOR,         //车牌彩色小图 02
            */
            strncpy(tImgObj.achType, "10", sizeof(tImgObj.achType) - 1);
            tImgObj.achType[sizeof(tImgObj.achType) - 1] = '\0';
            tImgObj.pSubImageBase64BinaryData = NULL;
            snprintf(tImgObj.achFileFormat, sizeof(tImgObj.achFileFormat), "%s", "jpeg");
            strncpy(tImgObj.achFilePath, "/application/pubsec/test.jpg", sizeof(tImgObj.achFilePath) - 1);
            tImgObj.achFilePath[sizeof(tImgObj.achFilePath) - 1] = '\0';

            PubsecClient* pubsecClient = PubsecClient::instance();
            pubsecClient->postImagePerson(0, &tPersonInfo, 1);
        }

        void Pubsec::postimageGather(const Json::Value &record)
        {
            // 使用RAII管理器自动管理内存
            ImageGatherManager gatherManager(1, 1, 1, 1);  // 1个图像、1个人员、1个人脸、1个门禁记录

            // 获取TPubSecObjList结构
            TPubSecObjList tImageGather = gatherManager.toObjList();

            // 初始化图像信息
            TPubSecImageObjInfo& imageInfo = gatherManager.getImage(0);
            

            char timeStr[32];
            if(record.isMember("createdAt")) {
                uint64_t eventTime = record["createdAt"].asUInt64();
        
                UBase::CTime cur = UBase::CTime(eventTime);
                char dateBuf[64] = {0};
                cur.format(dateBuf,"yyyyMMdd", UBase::CTime::fmDateFormat);
                infof("eventTimeStr datepart = %s\n", dateBuf);
                
                char timeBuf[64] = {0};
                cur.format(timeBuf,"HHmmss", UBase::CTime::fmHourFormat);
                infof("eventTimeStr timepart = %s\n", timeBuf);
        
                // 合并日期和时间，格式为yyyyMMddHHmmss
                snprintf(timeStr, sizeof(timeStr), "%s%s", dateBuf, timeBuf);
                infof("timeStr = %s\n", timeStr);
            }
            else {
                // 获取当前时间
                time_t now = time(0);
                struct tm *ltm = localtime(&now);
                
                strftime(timeStr, sizeof(timeStr), "%Y%m%d%H%M%S", ltm);
                infof("timeStr = %s\n", timeStr);
            }
            
            // 将recordId从字符串转换为整数
            int recordId = std::stoi(record["recordId"].asString());

            // 设置图像基本信息 - 使用SafeStringUtils进行安全字符串操作
            SafeStringUtils::safeFormat(imageInfo.achImageID, "%s%s%05d",
                m_pubsecCommonInfo[0].achDeviceID, timeStr, recordId);
            imageInfo.nInfoKind = PUBSEC_INTELLI_MAN_TYPE_MANUAL;  // 使用枚举类型
            SafeStringUtils::safeCopy(imageInfo.achImageSource, "17");  // ImageSource: "17"
            SafeStringUtils::safeCopy(imageInfo.achDeviceID, m_pubsecCommonInfo[0].achDeviceID);
            SafeStringUtils::safeCopy(imageInfo.achStoragePath, "");  // StoragePath: ""
            SafeStringUtils::safeCopy(imageInfo.achFileHash, "");  // FileHash: ""
            SafeStringUtils::safeCopy(imageInfo.achFileFormat, "Jpeg");  // FileFormat: "Jpeg"

            // 设置时间
            SafeStringUtils::safeCopy(imageInfo.achShotTime, timeStr);
            
            // 设置图像尺寸
            imageInfo.nWidth = 176;  // Width: 176
            imageInfo.nHeight = 202;  // Height: 202
            
            // 题名
            SafeStringUtils::safeCopy(imageInfo.achTitle, "门禁记录");

            // ContentDescription
            SafeStringUtils::safeCopy(imageInfo.achContentDescription, "三代门禁-人脸");

            // ShotPlaceFullAdress
            SafeStringUtils::safeCopy(imageInfo.achShotPlaceFullAdress, "门禁位置");
            SafeStringUtils::safeCopy(imageInfo.achSecurityLevel, "5");  // SecurityLevel: "5"
            
            // 添加example.json中的其他字段
            imageInfo.tApertureValue.bHas = true;
            imageInfo.tApertureValue.value = 23;  // ApertureValue: 23
            strncpy(imageInfo.achCameraManufacturer, "yf", sizeof(imageInfo.achCameraManufacturer) - 1);  // CameraManufacturer: "android"
            imageInfo.achCameraManufacturer[sizeof(imageInfo.achCameraManufacturer) - 1] = '\0';
            strncpy(imageInfo.achCameraVersion, "v1", sizeof(imageInfo.achCameraVersion) - 1);  // CameraVersion: "v1"
            imageInfo.achCameraVersion[sizeof(imageInfo.achCameraVersion) - 1] = '\0';
            strncpy(imageInfo.achCollectorID, "", sizeof(imageInfo.achCollectorID) - 1);  // CollectorID: ""
            imageInfo.achCollectorID[sizeof(imageInfo.achCollectorID) - 1] = '\0';
            strncpy(imageInfo.achCollectorIDType, "2", sizeof(imageInfo.achCollectorIDType) - 1);  // CollectorIDType: "2"
            imageInfo.achCollectorIDType[sizeof(imageInfo.achCollectorIDType) - 1] = '\0';
            strncpy(imageInfo.achCollectorName, "", sizeof(imageInfo.achCollectorName) - 1);  // CollectorName: ""
            imageInfo.achCollectorName[sizeof(imageInfo.achCollectorName) - 1] = '\0';
            strncpy(imageInfo.achCollectorOrg, "", sizeof(imageInfo.achCollectorOrg) - 1);  // CollectorOrg: ""
            imageInfo.achCollectorOrg[sizeof(imageInfo.achCollectorOrg) - 1] = '\0';
            strncpy(imageInfo.achEntryClrkOrg, "", sizeof(imageInfo.achEntryClrkOrg) - 1);  // EntryClerkOrg: ""
            imageInfo.achEntryClrkOrg[sizeof(imageInfo.achEntryClrkOrg) - 1] = '\0';
            strncpy(imageInfo.achEntryClrk, "", sizeof(imageInfo.achEntryClrk) - 1);  // EntryClrk: ""
            imageInfo.achEntryClrk[sizeof(imageInfo.achEntryClrk) - 1] = '\0';
            strncpy(imageInfo.achEntryClrkID, "", sizeof(imageInfo.achEntryClrkID) - 1);  // EntryClrkID: ""
            imageInfo.achEntryClrkID[sizeof(imageInfo.achEntryClrkID) - 1] = '\0';
            strncpy(imageInfo.achEntryClrkIDType, "", sizeof(imageInfo.achEntryClrkIDType) - 1);  // EntryClrkIDType: ""
            imageInfo.achEntryClrkIDType[sizeof(imageInfo.achEntryClrkIDType) - 1] = '\0';
            strncpy(imageInfo.achEntryTime, "", sizeof(imageInfo.achEntryTime) - 1);  // EntryTime: ""
            imageInfo.achEntryTime[sizeof(imageInfo.achEntryTime) - 1] = '\0';
            imageInfo.tEventSort.bHas = true;
            imageInfo.tEventSort.value = 11;  // EventSort: 11
            imageInfo.tFileSize.bHas = true;
            imageInfo.tFileSize.value = 0;  // FileSize: 0
            imageInfo.tFocalLength.bHas = true;
            imageInfo.tFocalLength.value = 100;  // FocalLength: 100
            strncpy(imageInfo.achHorizontalShotDirection, "", sizeof(imageInfo.achHorizontalShotDirection) - 1);  // HorizontalShotDirection: ""
            imageInfo.achHorizontalShotDirection[sizeof(imageInfo.achHorizontalShotDirection) - 1] = '\0';
            imageInfo.tISOSensitivity.bHas = true;
            imageInfo.tISOSensitivity.value = 230;  // ISOSensitivity: 230
            strncpy(imageInfo.achImgProcFlag, "0", sizeof(imageInfo.achImgProcFlag) - 1);  // ImgProcFlag: 0
            imageInfo.achImgProcFlag[sizeof(imageInfo.achImgProcFlag) - 1] = '\0';
            strncpy(imageInfo.achKeyWord, "img", sizeof(imageInfo.achKeyWord) - 1);  // KeyWord: "img"
            imageInfo.achKeyWord[sizeof(imageInfo.achKeyWord) - 1] = '\0';
            strncpy(imageInfo.achOriginImageID, "", sizeof(imageInfo.achOriginImageID) - 1);  // OriginImageID: ""
            imageInfo.achOriginImageID[sizeof(imageInfo.achOriginImageID) - 1] = '\0';
            strncpy(imageInfo.achQualityGrade, "5", sizeof(imageInfo.achQualityGrade) - 1);  // QualityGrade: "5"
            imageInfo.achQualityGrade[sizeof(imageInfo.achQualityGrade) - 1] = '\0';
            strncpy(imageInfo.achShotPlaceCode, "1", sizeof(imageInfo.achShotPlaceCode) - 1);  // ShotPlaceCode: "1"
            imageInfo.achShotPlaceCode[sizeof(imageInfo.achShotPlaceCode) - 1] = '\0';
            strncpy(imageInfo.achShotPlaceFullAdress, "1", sizeof(imageInfo.achShotPlaceFullAdress) - 1);  // ShotPlaceFullAdress: "1"
            imageInfo.achShotPlaceFullAdress[sizeof(imageInfo.achShotPlaceFullAdress) - 1] = '\0';
            imageInfo.tShotPlaceLatitude.bHas = true;
            imageInfo.tShotPlaceLatitude.value = 0;  // ShotPlaceLatitude: 0.0
            imageInfo.tShotPlaceLongitude.bHas = true;
            imageInfo.tShotPlaceLongitude.value = 0;  // ShotPlaceLongitude: 0.0
            strncpy(imageInfo.achSourceVideoID, "", sizeof(imageInfo.achSourceVideoID) - 1);  // SourceVideoID: ""
            imageInfo.achSourceVideoID[sizeof(imageInfo.achSourceVideoID) - 1] = '\0';
            strncpy(imageInfo.achSpecialIName, "", sizeof(imageInfo.achSpecialIName) - 1);  // SpecialIName: ""
            imageInfo.achSpecialIName[sizeof(imageInfo.achSpecialIName) - 1] = '\0';
            strncpy(imageInfo.achSubjectCharacter, "三代门禁-人脸", sizeof(imageInfo.achSubjectCharacter) - 1);  // SubjectCharacter
            imageInfo.achSubjectCharacter[sizeof(imageInfo.achSubjectCharacter) - 1] = '\0';
            strncpy(imageInfo.achTitleNote, "", sizeof(imageInfo.achTitleNote) - 1);  // TitleNote: ""
            imageInfo.achTitleNote[sizeof(imageInfo.achTitleNote) - 1] = '\0';
            strncpy(imageInfo.achVerticalShotDirection, "2", sizeof(imageInfo.achVerticalShotDirection) - 1);  // VerticalShotDirection: "2"
            imageInfo.achVerticalShotDirection[sizeof(imageInfo.achVerticalShotDirection) - 1] = '\0';

            // 初始化人员信息
            TPubSecPersonnelObjInfo& personInfo = gatherManager.getPersonnel(0);

            // 人员标识
            SafeStringUtils::safeCopy(personInfo.achPersonID, imageInfo.achImageID);

            // 信息分类
            personInfo.nInfoKind = imageInfo.nInfoKind;

            // 来源图像信息标识
            SafeStringUtils::safeCopy(personInfo.achSourceID, imageInfo.achImageID);

            // 设备编码
            SafeStringUtils::safeCopy(personInfo.achDeviceID, m_pubsecCommonInfo[0].achDeviceID);
            
            // 坐标信息（必选，但可以为0）
            personInfo.tLeftTopX.bHas = true;
            personInfo.tLeftTopX.value = 0;
            personInfo.tLeftTopY.bHas = true;
            personInfo.tLeftTopY.value = 0;
            personInfo.tRightBtmX.bHas = true;
            personInfo.tRightBtmX.value = imageInfo.nWidth;
            personInfo.tRightBtmY.bHas = true;
            personInfo.tRightBtmY.value = imageInfo.nHeight;
            
            // 设置人员子图像 - 使用RAII管理器分配
            SubImageListManager* subImageManager = gatherManager.allocateSubImages(0, 1);
            if (!subImageManager) {
                errorf("Failed to allocate sub images\n");
                return;
            }
            TPubSecSubImageList& tPersonSubImg = (*subImageManager)[0];

            std::string spotImgpath;
            std::string cropImgpath;
            if (record.isMember("spotContent")) {
                const Json::Value& spotContent = record["spotContent"];

                // 图像文件路径
                if (spotContent.isMember("temp") && spotContent["temp"].isMember("spotImage")) {
                    SafeStringUtils::safeCopy(imageInfo.achFilePath, spotContent["temp"]["spotImage"].asString());
                    spotImgpath = spotContent["temp"]["spotImage"].asString();
                    infof("spotImgpath = %s\n", spotImgpath.c_str());
                }

                if (spotContent.isMember("temp") && spotContent["temp"].isMember("cropImage")) {
                    SafeStringUtils::safeCopy(imageInfo.achFilePath, spotContent["temp"]["cropImage"].asString());
                    cropImgpath = spotContent["temp"]["cropImage"].asString();
                    infof("cropImgpath = %s\n", cropImgpath.c_str());
                }
                
            }
            
            // 设置子图像信息 - 使用example.json中的格式
            strncpy(tPersonSubImg.achImageID, imageInfo.achImageID, sizeof(tPersonSubImg.achImageID) - 1);
            tPersonSubImg.achImageID[sizeof(tPersonSubImg.achImageID) - 1] = '\0';
            strncpy(tPersonSubImg.achDeviceID, m_pubsecCommonInfo[0].achDeviceID, sizeof(tPersonSubImg.achDeviceID) - 1);
            tPersonSubImg.achDeviceID[sizeof(tPersonSubImg.achDeviceID) - 1] = '\0';
            strncpy(tPersonSubImg.achStoragePath, "http://", sizeof(tPersonSubImg.achStoragePath) - 1);  // StoragePath: "http://"
            tPersonSubImg.achStoragePath[sizeof(tPersonSubImg.achStoragePath) - 1] = '\0';
            strncpy(tPersonSubImg.achType, "11", sizeof(tPersonSubImg.achType) - 1);  // Type: "11"
            tPersonSubImg.achType[sizeof(tPersonSubImg.achType) - 1] = '\0';
            strncpy(tPersonSubImg.achFileFormat, "Jpeg", sizeof(tPersonSubImg.achFileFormat) - 1);  // FileFormat: "Jpeg"
            tPersonSubImg.achFileFormat[sizeof(tPersonSubImg.achFileFormat) - 1] = '\0';
            strncpy(tPersonSubImg.achShotTime, timeStr, sizeof(tPersonSubImg.achShotTime) - 1);
            tPersonSubImg.achShotTime[sizeof(tPersonSubImg.achShotTime) - 1] = '\0';
            tPersonSubImg.nWidth = 176;  // Width: 176
            tPersonSubImg.nHeight = 202;  // Height: 202
            SafeStringUtils::safeCopy(tPersonSubImg.achFilePath, spotImgpath);  // 保持图片路径不变

            // 初始化人脸信息 - 注意example.json中没有FaceList，但我们保留它
            TPubSecFaceObjInfo& faceInfo = gatherManager.getFace(0);

            // 设置人脸基本信息
            SafeStringUtils::safeCopy(faceInfo.achsFaceID, imageInfo.achImageID);
            faceInfo.nInfoKind = PUBSEC_INTELLI_MAN_TYPE_MANUAL;  // 使用枚举类型
            SafeStringUtils::safeCopy(faceInfo.achSourceID, imageInfo.achImageID);
            SafeStringUtils::safeCopy(faceInfo.achDeviceID, imageInfo.achDeviceID);

            // 设置时间信息
            SafeStringUtils::safeCopy(faceInfo.achShotTime, timeStr);
            SafeStringUtils::safeCopy(faceInfo.achLocationMarkTime, timeStr);
            strncpy(faceInfo.achFaceAppearTime, timeStr, sizeof(faceInfo.achFaceAppearTime) - 1);
            faceInfo.achFaceAppearTime[sizeof(faceInfo.achFaceAppearTime) - 1] = '\0';
            strncpy(faceInfo.achFaceDisAppearTime, timeStr, sizeof(faceInfo.achFaceDisAppearTime) - 1);
            faceInfo.achFaceDisAppearTime[sizeof(faceInfo.achFaceDisAppearTime) - 1] = '\0';

            // 初始化门禁记录
            TPubSecAccessRecord& accessRecord = gatherManager.getAccessRecord(0);

            // 设置门禁记录信息 - 使用SafeStringUtils进行安全字符串操作
            SafeStringUtils::safeCopy(accessRecord.achAccessRecordID, imageInfo.achImageID);
            SafeStringUtils::safeCopy(accessRecord.achSourceID, imageInfo.achImageID);
            SafeStringUtils::safeCopy(accessRecord.achDeviceID, m_pubsecCommonInfo[0].achDeviceID);
            // SecureAccessID在example.json中不存在，保留原值
            SafeStringUtils::safeCopy(accessRecord.achSecureAccessID, "secure_test");
            SafeStringUtils::safeCopy(accessRecord.achAlarmEventCode, "00-01");  // AlarmEventCode: "00-01"
            accessRecord.dwCardID = 0;  // CardID: 0
            accessRecord.nCardType = 1;  // CardType: 1
            SafeStringUtils::safeCopy(accessRecord.achPersonCode, "3424");  // PersonCode: "3424"
            SafeStringUtils::safeCopy(accessRecord.achAccessTime, timeStr);  // AccessTime
            // 添加example.json中的EntryTime字段
            SafeStringUtils::safeCopy(accessRecord.achEntryTime, timeStr);  // EntryTime
            SafeStringUtils::safeCopy(accessRecord.achPunchTime, timeStr);  // PunchTime
            accessRecord.nPunchAct = 1;  // PunchAct: 1
            accessRecord.nPunchResult = 1;  // PunchResult: 1

            // 调用客户端发送数据
            PubsecClient* pubsecClient = PubsecClient::instance();
            pubsecClient->postImageGather(0, &tImageGather);

            // 注意：内存由RAII管理器自动释放，无需手动释放
        }

        void Pubsec::onConfig(const char *name, const char *client, const Json::Value &config) {
            warnf(">>> platform config change : %s  \n ",config.toStyledString().c_str());

            // 解析新的配置参数
            jsonToParam(config, &m_pubsecCommonInfoNew[0]);
            
            // 检查关键参数是否发生变化
            {
                std::lock_guard<std::mutex> lock(m_registerMutex);
                
                // 检查关键参数变化 - 使用内存比较提高效率
                if (memcmp(&m_pubsecCommonInfo[0], &m_pubsecCommonInfoNew[0], sizeof(TPubSecCommonObjInfo)) != 0) {
                    
                    // 更新配置
                    memcpy(&m_pubsecCommonInfo[0], &m_pubsecCommonInfoNew[0], sizeof(TPubSecCommonObjInfo));
                    
                    // 标记配置已变更，需要重新注册
                    infof("Config changed, need to re-register\n");
                    m_cfgChange = true;
                    // 通知注册线程
                    m_registerCV.notify_one();
                }
            }

            return;
        }

        std::string snapImage() {
            using namespace AlgReactor;
            using namespace Resource;
            
            Media::VideoPacket packet;
            if (!IAlgSnapshot::instance()->snapStream(packet) 
                || !packet.valid()) {
                errorf("snap img failed ! \n");
                return "";
            }
        
            std::string name = "snapShot";
            std::string path = ResourceManager::instance()->getWorkPath("cachePath");
            std::string filePath = ResourceManager::instance()->createFile(path.c_str(),name.c_str(),"jpg",packet);    
            if (filePath.empty()) { 
                errorf("snap failed ! \n");
                return "";
            }
        
            debugf("snap success, pic path : %s \n", filePath.c_str());   
        
            UBase::File::syncFile();
            // UBase::File::remove(filePath.c_str());
        
            return filePath;
        }

        void Pubsec::onAlarm(AppEvent::EventSubType type, const Json::Value &data) {
            umpDebugf("alarm data : %s \n",data.toStyledString().c_str());
            if (type != AppEvent::EventSubType::systemAlarm) {
                return;
            }
            #if 0
            // 定义记录结构
            Json::Value record;
            record["createdAt"] = UBase::CTime::getCurrentMilliSecond();  // 创建时间戳
            record["elemType"] = 0;   // 元素类型
            record["failType"] = 0;               // 失败类型
            record["mode"] = 0;                   // 模式
            record["name"] = "";              // 名称
            record["personId"] = "";      // 人员ID
            record["recordId"] = "";            // 记录ID
            record["remoteId"] = "";             // 远程ID
            record["result"] = 0;                 // 结果
            record["score"] = 0;                  // 分数
            record["spotType"] = 0;               // 现场类型
            record["type"] = 0;                   // 类型
            record["uploadStatus"] = 0;           // 上传状态

            // 设置spotContent子对象
            Json::Value spotContent;
            spotContent["controlType"] = "";           // 控制类型
            spotContent["endAt"] = 0;        // 结束时间
            spotContent["idNumber"] = "";                // 身份证号
            spotContent["identityType"] = "";         // 身份类型

            spotContent["cropImage"] = "";    // 裁剪图片路径
            spotContent["spotImage"] = snapImage();     // 现场图片路径

            // 组装完整的JSON结构
            record["spotContent"] = spotContent;
            onRecord(record);
        #endif

        /*
            int32_t alarmType = data["eventType"].asInt();
            Json::Value msg = Json::nullValue;
            Json::Value param = Json::nullValue;
            
            param["createdAt"] = UBase::CTime::getCurrentUTCtime();
            if(alarmType == System::IAlarmCtrl::antiTamperEvent){
                param[EVENTCODE] = ANTI_THEFT_EVENT;
                param[PERSONID] = "";
                
                //事件需要图片信息,应用进行抓图上报
                std::string base64;
                if(PlatformDataHandler::instance()->snapImageToBase64(base64)){
                    param["base64"] = base64;
                }
        
                PlatformMessageHandler::instance()->createEventLsnInfMsg(msg,param);
            }else if(alarmType == System::IAlarmCtrl::fireEvent) {
                param[EVENTCODE] = FIRE_ALARM_EVENT;
                param[PERSONID] = "";
        
                //事件需要图片信息,应用进行抓图上报            
                std::string base64;
                if(PlatformDataHandler::instance()->snapImageToBase64(base64)){
                    param["base64"] = base64;
                }
        
                PlatformMessageHandler::instance()->createEventLsnInfMsg(msg,param); 
                if(!sendData(msg)) {
                    errorf(" send failed ! \n");        
                }
        
                //还需上报一次常开门禁状态
                {        
                    msg = Json::nullValue;
                    param[DOORTYPE] = DOOR_NORMALLY_OPEN;
                    PlatformMessageHandler::instance()->createDevSensRptMsg(msg,param);
                }
        
            }else {
                errorf("unsupport type : %d \n", alarmType);
                return ;
            }
            */

        }

        bool Pubsec::onRecord(const Json::Value &record) {

            EPubSecCltRet ret = PUBSEC_CLT_OK;
            //未连接平台成功注册, 设置重传标识, 1分钟检测一次，一次最多重传60条
            if(!m_registered) {

                //重传离线期间所有的记录。
                std::string recordId;
                if( !record.empty() 
                    && record.isObject() ) {
                    recordId = record.isMember("recordId") ? record["recordId"].asString() : "";
                }

                umpDebugf("not register to platform !!!  recordId [%s]  wait reupload \n", recordId.c_str());
                return false;
            }

            umpDebugf("mm@ pubsec onRecordEvent: %s \n", record.toStyledString().c_str());

            // 使用RAII管理器自动管理内存
            ImageGatherManager gatherManager(1, 1, 0, 1);  // 1个图像、1个人员、0个人脸、1个门禁记录

            // 获取TPubSecObjList结构
            TPubSecObjList tImageGather = gatherManager.toObjList();

            // 使用新的RAII版本的事件处理方法
            jsonToEventRAII(record, gatherManager);

            // 调用客户端发送数据
            PubsecClient* pubsecClient = PubsecClient::instance();
            if (pubsecClient) {
                ret = pubsecClient->postImageGather(0, &tImageGather);
            }
            umpDebugf("mm@ pubsec report ret : %d \n", PUBSEC_CLT_OK == ret);

            // 注意：内存由RAII管理器自动释放，无需手动释放

            const Json::Value &spot = record["spotContent"];
            if(spot.empty()
                || !spot.isMember("temp")){
                return PUBSEC_CLT_OK == ret;
            }
            else
            {
                if (PUBSEC_CLT_OK == ret)
                {
                    AlgReactor::IRecordService::instance()->deleteSpotImage(spot["temp"]);
                }
            }

            return PUBSEC_CLT_OK == ret;
        }

        void Pubsec::onData(const Json::Value &data) {
            if (!data.isMember("action") && !data["action"].isInt()) {
                return;
            }
        }

        // 从JSON解析参数到TPubSecCommonObjInfo结构体
        void Pubsec::jsonToParam(const Json::Value& pubsecCfg, TPubSecCommonObjInfo* pstCommonObjInfo)
        {
            if (!pstCommonObjInfo || pubsecCfg.empty()) {
                errorf("Invalid parameters for jsonToParam\n");
                return;
            }
            
            // 清空结构体
            memset(pstCommonObjInfo, 0, sizeof(TPubSecCommonObjInfo));
            
            // 从配置中读取并设置各个参数
            pstCommonObjInfo->bEnable = pubsecCfg["enable"].asBool();
            
            // 设置用户信息 - 使用安全的字符串复制
            std::string username = pubsecCfg["username"].asString();
            std::string password = pubsecCfg["password"].asString();
            std::string deviceId = pubsecCfg["deviceId"].asString();
            std::string platformAddr = pubsecCfg["platformAddress"].asString();

            // 安全复制用户信息，确保字符串正确终止
            strncpy(pstCommonObjInfo->tUserInfo.achUser, username.c_str(), PUBSEC_STRING_LENGTH_SMALLER_BYTE - 1);
            pstCommonObjInfo->tUserInfo.achUser[PUBSEC_STRING_LENGTH_SMALLER_BYTE - 1] = '\0';

            strncpy(pstCommonObjInfo->tUserInfo.achKey, password.c_str(), PUBSEC_STRING_LENGTH_SMALLER_BYTE - 1);
            pstCommonObjInfo->tUserInfo.achKey[PUBSEC_STRING_LENGTH_SMALLER_BYTE - 1] = '\0';

            // 安全复制设备ID
            strncpy(pstCommonObjInfo->achDeviceID, deviceId.c_str(), PUBSEC_DeviceIDType - 1);
            pstCommonObjInfo->achDeviceID[PUBSEC_DeviceIDType - 1] = '\0';

            // 安全复制服务器地址
            strncpy(pstCommonObjInfo->tServerIPAddress.achIPV4, platformAddr.c_str(), PUBSEC_STRING_LENGTH_TINY_BYTE - 1);
            pstCommonObjInfo->tServerIPAddress.achIPV4[PUBSEC_STRING_LENGTH_TINY_BYTE - 1] = '\0';
            
            // 设置端口
            if (pubsecCfg.isMember("videoLibPort") && !pubsecCfg["videoLibPort"].asString().empty()) {
                try {
                    pstCommonObjInfo->tServerIPAddress.nPort = std::stoi(pubsecCfg["videoLibPort"].asString());
                } catch (const std::exception& e) {
                    errorf("Invalid port format, using default: %s\n", e.what());
                    pstCommonObjInfo->tServerIPAddress.nPort = 8080; // 默认端口
                }
            } else {
                pstCommonObjInfo->tServerIPAddress.nPort = 8080; // 默认端口
            }
            
            // 设置同步时间间隔
            if (pubsecCfg.isMember("syncInterval") && !pubsecCfg["syncInterval"].asString().empty()) {
                try {
                    pstCommonObjInfo->dwSyncTimeInterval = std::stoi(pubsecCfg["syncInterval"].asString());
                } catch (const std::exception& e) {
                    errorf("Invalid syncInterval format, using default: %s\n", e.what());
                    pstCommonObjInfo->dwSyncTimeInterval = 3600; // 默认1小时
                }
            } else {
                pstCommonObjInfo->dwSyncTimeInterval = 3600; // 默认1小时
            }
            
            // 设置心跳时间
            if (pubsecCfg.isMember("heartbeatInterval")) {
                pstCommonObjInfo->nKeepAliveTime = pubsecCfg["heartbeatInterval"].asInt();
            } else {
                pstCommonObjInfo->nKeepAliveTime = 60; // 默认60秒
            }
            
            // 设置传输方式 (默认为HTTP)
            pstCommonObjInfo->eTransMeth = PUBSEC_HTTP;
            
            infof("Pubsec parameters loaded: enable=%d, username=%s, password=%s, deviceId=%s, server=%s:%d, heartbeat=%d, syncInterval=%u\n",
                  pstCommonObjInfo->bEnable,
                  pstCommonObjInfo->tUserInfo.achUser,
                  pstCommonObjInfo->tUserInfo.achKey,
                  pstCommonObjInfo->achDeviceID,
                  pstCommonObjInfo->tServerIPAddress.achIPV4,
                  pstCommonObjInfo->tServerIPAddress.nPort,
                  pstCommonObjInfo->nKeepAliveTime,
                  pstCommonObjInfo->dwSyncTimeInterval);
        }

        bool readJpegDimensions(const std::string& filePath, int& width, int& height) {
            std::ifstream file(filePath, std::ios::binary);
            if (!file.is_open()) {
                errorf("无法打开文件: %s\n", filePath.c_str());
                return false;
            }

            // 读取文件内容到缓冲区
            size_t bufferSize = 4 * 1024;  // 4KB

            std::vector<unsigned char> buffer(bufferSize);
            file.read(reinterpret_cast<char*>(buffer.data()), bufferSize);
            size_t actualSize = file.gcount();  // 获取实际读取的字节数
            file.close();

            // 检查 JPEG 文件头 (0xFFD8)
            if (actualSize < 2 || buffer[0] != 0xFF || buffer[1] != 0xD8) {
                errorf("不是有效的 JPEG 文件: %s\n", filePath.c_str());
                return false;
            }

            size_t pos = 2; // 从文件头后开始
            while (pos < actualSize - 1) {
                // 寻找下一个标记
                if (buffer[pos] != 0xFF) {
                    errorf("无效的 JPEG 标记 at pos %zu in file: %s\n", pos, filePath.c_str());
                    return false;
                }

                if (pos + 1 >= actualSize) {
                    errorf("缓冲区边界检查失败 at pos %zu in file: %s\n", pos, filePath.c_str());
                    return false;
                }

                unsigned char marker = buffer[pos + 1];
                pos += 2;

                // SOF 标记 (0xC0 到 0xCF，排除 0xC4, 0xC8, 0xCC)
                if ((marker >= 0xC0 && marker <= 0xCF) && marker != 0xC4 && marker != 0xC8 && marker != 0xCC) {
                    // 确保有足够数据读取宽度和高度
                    if (pos + 7 > actualSize) {
                        errorf("SOF 段数据不足 in file: %s\n", filePath.c_str());
                        return false;
                    }

                    // 高度 (2 字节)
                    height = (buffer[pos + 1] << 8) | buffer[pos + 2];
                    // 宽度 (2 字节)
                    width = (buffer[pos + 3] << 8) | buffer[pos + 4];
                    return true;
                } else {
                    // 其他段，读取段长度并跳过
                    if (pos + 1 >= actualSize) {
                        errorf("段长度数据不足 at pos %zu in file: %s\n", pos, filePath.c_str());
                        return false;
                    }
                    size_t length = (buffer[pos] << 8) | buffer[pos + 1];
                    if (length < 2) {
                        errorf("无效的段长度 %zu at pos %zu in file: %s\n", length, pos, filePath.c_str());
                        return false;
                    }
                    pos += length;
                }
            }

            errorf("未找到 SOF 段 in file: %s\n", filePath.c_str());
            return false;
        }
        void Pubsec::jsonToEvent(const Json::Value& record, TPubSecObjList* ptImageGather)
        {
            if (!ptImageGather || record.empty()) {
                errorf("Invalid parameters\n");
                return;
            }

            if (!record.isMember("recordId") || !record["recordId"].isString()) {
                errorf("Invalid recordId format\n");
                return;
            }

            std::string spotImgpath;
            std::string cropImgpath;
            // 清空结构体
            memset(ptImageGather, 0, sizeof(TPubSecObjList));
            
            // 设置图像数量
            ptImageGather->achObjectDataMSG[1][0] = 1;  // 图像数量
            ptImageGather->achObjectDataMSG[1][1] = 1;  // 人员数量
            ptImageGather->achObjectDataMSG[1][2] = 1;  // 人脸数量

            // 分配内存
            ptImageGather->ptPubSecImageList = new TPubSecImageObjInfo[1];
            ptImageGather->ptPersonnelObjlist = new TPubSecPersonnelObjInfo[1];
            ptImageGather->ptWisComAcceRec = new TPubSecAccessRecord[1];
            
            // 初始化图像信息
            TPubSecImageObjInfo& imageInfo = ptImageGather->ptPubSecImageList[0];
            memset(&imageInfo, 0, sizeof(imageInfo));
            
            char timeStr[32];
            if(record.isMember("createdAt")) {
                uint64_t eventTime = record["createdAt"].asUInt64();
        
                UBase::CTime cur = UBase::CTime(eventTime);
                char dateBuf[64] = {0};
                cur.format(dateBuf,"yyyyMMdd", UBase::CTime::fmDateFormat);
                infof("eventTimeStr datepart = %s\n", dateBuf);
                
                char timeBuf[64] = {0};
                cur.format(timeBuf,"HHmmss", UBase::CTime::fmHourFormat);
                infof("eventTimeStr timepart = %s\n", timeBuf);
        
                // 合并日期和时间，格式为yyyyMMddHHmmss
                snprintf(timeStr, sizeof(timeStr), "%s%s", dateBuf, timeBuf);
                infof("timeStr = %s\n", timeStr);
            }
            else {
                // 获取当前时间
                time_t now = time(0);
                struct tm *ltm = localtime(&now);
                
                strftime(timeStr, sizeof(timeStr), "%Y%m%d%H%M%S", ltm);
                infof("timeStr = %s\n", timeStr);
            }
            
            // 将recordId从字符串转换为整数
            int recordId = std::stoi(record["recordId"].asString());

            // 生成图像ID
            snprintf(imageInfo.achImageID, PUBSEC_BasicObjectIdType, "%s%s%05d", 
                     m_pubsecCommonInfo[0].achDeviceID, timeStr, 
                     recordId);
            imageInfo.achImageID[PUBSEC_BasicObjectIdType - 1] = '\0';

            // 基本信息设置
            imageInfo.nInfoKind = PUBSEC_INTELLI_MAN_TYPE_MANUAL;  // 手动采集
            strncpy(imageInfo.achImageSource, "17", sizeof(imageInfo.achImageSource) - 1);  // 一般为"17"
            imageInfo.achImageSource[sizeof(imageInfo.achImageSource) - 1] = '\0';
            strncpy(imageInfo.achDeviceID, m_pubsecCommonInfo[0].achDeviceID, sizeof(imageInfo.achDeviceID) - 1);
            imageInfo.achDeviceID[sizeof(imageInfo.achDeviceID) - 1] = '\0';

            // 原始图像标识
            if (record.isMember("recordId") && !record["recordId"].asString().empty()) {
                strncpy(imageInfo.achOriginImageID, record["recordId"].asString().c_str(), sizeof(imageInfo.achOriginImageID) - 1);
                imageInfo.achOriginImageID[sizeof(imageInfo.achOriginImageID) - 1] = '\0';
            } else {
                strncpy(imageInfo.achOriginImageID, imageInfo.achImageID, sizeof(imageInfo.achOriginImageID) - 1);
                imageInfo.achOriginImageID[sizeof(imageInfo.achOriginImageID) - 1] = '\0';
            }

            // 事件类型
            imageInfo.tEventSort.bHas = true;
            imageInfo.tEventSort.value = 11;  // 门禁事件

            // 文件格式和时间
            strncpy(imageInfo.achFileFormat, "Jpeg", sizeof(imageInfo.achFileFormat) - 1);
            imageInfo.achFileFormat[sizeof(imageInfo.achFileFormat) - 1] = '\0';
            strncpy(imageInfo.achShotTime, timeStr, sizeof(imageInfo.achShotTime) - 1);
            imageInfo.achShotTime[sizeof(imageInfo.achShotTime) - 1] = '\0';

            // 题名
            strncpy(imageInfo.achTitle, "门禁记录", sizeof(imageInfo.achTitle) - 1);
            imageInfo.achTitle[sizeof(imageInfo.achTitle) - 1] = '\0';
            snprintf(imageInfo.achContentDescription, sizeof(imageInfo.achContentDescription), "三代门禁-人脸");
            strncpy(imageInfo.achSubjectCharacter, "三代门禁-人脸", sizeof(imageInfo.achSubjectCharacter) - 1);
            imageInfo.achSubjectCharacter[sizeof(imageInfo.achSubjectCharacter) - 1] = '\0';

            // 拍摄地点
            strncpy(imageInfo.achShotPlaceFullAdress, "门禁位置", sizeof(imageInfo.achShotPlaceFullAdress) - 1);
            imageInfo.achShotPlaceFullAdress[sizeof(imageInfo.achShotPlaceFullAdress) - 1] = '\0';
            strncpy(imageInfo.achShotPlaceCode, "1", sizeof(imageInfo.achShotPlaceCode) - 1);
            imageInfo.achShotPlaceCode[sizeof(imageInfo.achShotPlaceCode) - 1] = '\0';
            imageInfo.tShotPlaceLatitude.bHas = true;
            imageInfo.tShotPlaceLatitude.value = 0;
            imageInfo.tShotPlaceLongitude.bHas = true;
            imageInfo.tShotPlaceLongitude.value = 0;

            // 安全级别
            strncpy(imageInfo.achSecurityLevel, "5", sizeof(imageInfo.achSecurityLevel) - 1);
            imageInfo.achSecurityLevel[sizeof(imageInfo.achSecurityLevel) - 1] = '\0';

            // 设备信息
            strncpy(imageInfo.achCameraManufacturer, "yf", sizeof(imageInfo.achCameraManufacturer) - 1);
            imageInfo.achCameraManufacturer[sizeof(imageInfo.achCameraManufacturer) - 1] = '\0';
            strncpy(imageInfo.achCameraVersion, "v1", sizeof(imageInfo.achCameraVersion) - 1);
            imageInfo.achCameraVersion[sizeof(imageInfo.achCameraVersion) - 1] = '\0';
            imageInfo.tApertureValue.bHas = true;
            imageInfo.tApertureValue.value = 23;
            imageInfo.tFocalLength.bHas = true;
            imageInfo.tFocalLength.value = 100;
            imageInfo.tISOSensitivity.bHas = true;
            imageInfo.tISOSensitivity.value = 230;

            // 采集信息
            strncpy(imageInfo.achCollectorID, "", sizeof(imageInfo.achCollectorID) - 1);
            imageInfo.achCollectorID[sizeof(imageInfo.achCollectorID) - 1] = '\0';
            strncpy(imageInfo.achCollectorIDType, "2", sizeof(imageInfo.achCollectorIDType) - 1);
            imageInfo.achCollectorIDType[sizeof(imageInfo.achCollectorIDType) - 1] = '\0';
            strncpy(imageInfo.achCollectorName, "", sizeof(imageInfo.achCollectorName) - 1);
            imageInfo.achCollectorName[sizeof(imageInfo.achCollectorName) - 1] = '\0';
            strncpy(imageInfo.achCollectorOrg, "", sizeof(imageInfo.achCollectorOrg) - 1);
            imageInfo.achCollectorOrg[sizeof(imageInfo.achCollectorOrg) - 1] = '\0';

            // 录入信息
            strncpy(imageInfo.achEntryClrkOrg, "", sizeof(imageInfo.achEntryClrkOrg) - 1);
            imageInfo.achEntryClrkOrg[sizeof(imageInfo.achEntryClrkOrg) - 1] = '\0';
            strncpy(imageInfo.achEntryClrk, "", sizeof(imageInfo.achEntryClrk) - 1);
            imageInfo.achEntryClrk[sizeof(imageInfo.achEntryClrk) - 1] = '\0';
            strncpy(imageInfo.achEntryClrkID, "", sizeof(imageInfo.achEntryClrkID) - 1);
            imageInfo.achEntryClrkID[sizeof(imageInfo.achEntryClrkID) - 1] = '\0';
            strncpy(imageInfo.achEntryClrkIDType, "", sizeof(imageInfo.achEntryClrkIDType) - 1);
            imageInfo.achEntryClrkIDType[sizeof(imageInfo.achEntryClrkIDType) - 1] = '\0';
            strncpy(imageInfo.achEntryTime, timeStr, sizeof(imageInfo.achEntryTime) - 1);
            imageInfo.achEntryTime[sizeof(imageInfo.achEntryTime) - 1] = '\0';

            // 其他属性
            strncpy(imageInfo.achImgProcFlag, "0", sizeof(imageInfo.achImgProcFlag) - 1);
            imageInfo.achImgProcFlag[sizeof(imageInfo.achImgProcFlag) - 1] = '\0';
            strncpy(imageInfo.achKeyWord, "img", sizeof(imageInfo.achKeyWord) - 1);
            imageInfo.achKeyWord[sizeof(imageInfo.achKeyWord) - 1] = '\0';
            strncpy(imageInfo.achQualityGrade, "5", sizeof(imageInfo.achQualityGrade) - 1);
            imageInfo.achQualityGrade[sizeof(imageInfo.achQualityGrade) - 1] = '\0';
            strncpy(imageInfo.achVerticalShotDirection, "2", sizeof(imageInfo.achVerticalShotDirection) - 1);
            imageInfo.achVerticalShotDirection[sizeof(imageInfo.achVerticalShotDirection) - 1] = '\0';
            strncpy(imageInfo.achHorizontalShotDirection, "", sizeof(imageInfo.achHorizontalShotDirection) - 1);
            imageInfo.achHorizontalShotDirection[sizeof(imageInfo.achHorizontalShotDirection) - 1] = '\0';
            strncpy(imageInfo.achSpecialIName, "", sizeof(imageInfo.achSpecialIName) - 1);
            imageInfo.achSpecialIName[sizeof(imageInfo.achSpecialIName) - 1] = '\0';
            strncpy(imageInfo.achTitleNote, "", sizeof(imageInfo.achTitleNote) - 1);
            imageInfo.achTitleNote[sizeof(imageInfo.achTitleNote) - 1] = '\0';
            strncpy(imageInfo.achSourceVideoID, "", sizeof(imageInfo.achSourceVideoID) - 1);
            imageInfo.achSourceVideoID[sizeof(imageInfo.achSourceVideoID) - 1] = '\0';

            // 文件大小
            imageInfo.tFileSize.bHas = true;
            imageInfo.tFileSize.value = 0;

            // 图像尺寸设置
            imageInfo.nWidth = 1280;  // 默认值
            imageInfo.nHeight = 720;  // 默认值

            // 从spotContent中获取图片路径并读取实际尺寸
            if (record.isMember("spotContent")) {
                const Json::Value& temp = record["spotContent"].isMember("temp") ? record["spotContent"]["temp"] : record["spotContent"];

                if (temp.isMember("spotImage")) {
                    spotImgpath = temp["spotImage"].asString();
                    strncpy(imageInfo.achFilePath, spotImgpath.c_str(), sizeof(imageInfo.achFilePath) - 1);
                    imageInfo.achFilePath[sizeof(imageInfo.achFilePath) - 1] = '\0';
                }
                // 备选使用cropImage
                else if (temp.isMember("cropImage")) {
                    cropImgpath = temp["cropImage"].asString();
                    strncpy(imageInfo.achFilePath, cropImgpath.c_str(), sizeof(imageInfo.achFilePath) - 1);
                    imageInfo.achFilePath[sizeof(imageInfo.achFilePath) - 1] = '\0';
                }
                
                // 读取图片获取实际尺寸
                if (!spotImgpath.empty()) {
                    bool result = readJpegDimensions(spotImgpath, imageInfo.nWidth, imageInfo.nHeight);
                    umpDebugf("file parse %d 读取图片尺寸: %dx%d\n", result, imageInfo.nWidth, imageInfo.nHeight);
                }
            }

            // 人员信息填充
            TPubSecPersonnelObjInfo& personInfo = ptImageGather->ptPersonnelObjlist[0];
            memset(&personInfo, 0, sizeof(personInfo));
            
            // 基本信息设置
            strncpy(personInfo.achPersonID, imageInfo.achImageID, sizeof(personInfo.achPersonID) - 1);
            personInfo.achPersonID[sizeof(personInfo.achPersonID) - 1] = '\0';
            personInfo.nInfoKind = imageInfo.nInfoKind;
            strncpy(personInfo.achSourceID, imageInfo.achImageID, sizeof(personInfo.achSourceID) - 1);
            personInfo.achSourceID[sizeof(personInfo.achSourceID) - 1] = '\0';
            strncpy(personInfo.achDeviceID, m_pubsecCommonInfo[0].achDeviceID, sizeof(personInfo.achDeviceID) - 1);
            personInfo.achDeviceID[sizeof(personInfo.achDeviceID) - 1] = '\0';

            // 坐标信息
            personInfo.tLeftTopX.bHas = true;
            personInfo.tLeftTopX.value = 0;
            personInfo.tLeftTopY.bHas = true;
            personInfo.tLeftTopY.value = 0;
            personInfo.tRightBtmX.bHas = true;
            personInfo.tRightBtmX.value = imageInfo.nWidth;
            personInfo.tRightBtmY.bHas = true;
            personInfo.tRightBtmY.value = imageInfo.nHeight;

            // 时间信息
            strncpy(personInfo.achLocationMarkTime, imageInfo.achShotTime, sizeof(personInfo.achLocationMarkTime) - 1);
            personInfo.achLocationMarkTime[sizeof(personInfo.achLocationMarkTime) - 1] = '\0';
            strncpy(personInfo.achPersonAppearTime, imageInfo.achShotTime, sizeof(personInfo.achPersonAppearTime) - 1);
            personInfo.achPersonAppearTime[sizeof(personInfo.achPersonAppearTime) - 1] = '\0';
            strncpy(personInfo.achPersonDisAppearTime, imageInfo.achShotTime, sizeof(personInfo.achPersonDisAppearTime) - 1);
            personInfo.achPersonDisAppearTime[sizeof(personInfo.achPersonDisAppearTime) - 1] = '\0';

            // 从spotContent中获取人员详细信息
            if (record.isMember("spotContent")) {
                const Json::Value& spotContent = record["spotContent"];
                
                // 证件号码
                if (spotContent.isMember("idNumber")) {
                    strncpy(personInfo.achIDNumber, spotContent["idNumber"].asString().c_str(), sizeof(personInfo.achIDNumber) - 1);
                    personInfo.achIDNumber[sizeof(personInfo.achIDNumber) - 1] = '\0';
                }
                
                // 证件类型
                if (spotContent.isMember("identityType")) {
                    const std::string& idType = spotContent["identityType"].asString();
                    if (idType == "ID_CARD") {
                        strncpy(personInfo.achIDType, "111", sizeof(personInfo.achIDType) - 1); // 居民身份证
                        personInfo.achIDType[sizeof(personInfo.achIDType) - 1] = '\0';
                    } else if (idType == "PASSPORT") {
                        strncpy(personInfo.achIDType, "112", sizeof(personInfo.achIDType) - 1); // 护照
                        personInfo.achIDType[sizeof(personInfo.achIDType) - 1] = '\0';
                    } else if (idType == "OFFICER") {
                        strncpy(personInfo.achIDType, "113", sizeof(personInfo.achIDType) - 1); // 军官证
                        personInfo.achIDType[sizeof(personInfo.achIDType) - 1] = '\0';
                    } else {
                        strncpy(personInfo.achIDType, "114", sizeof(personInfo.achIDType) - 1); // 其他证件
                        personInfo.achIDType[sizeof(personInfo.achIDType) - 1] = '\0';
                    }
                }
            }

            // 从record中获取姓名
            if (record.isMember("name")) {
                strncpy(personInfo.achName, record["name"].asString().c_str(), sizeof(personInfo.achName) - 1);
                personInfo.achName[sizeof(personInfo.achName) - 1] = '\0';
            }

            // 人员特征标记
            personInfo.tIsDriver.bHas = true;
            personInfo.tIsDriver.value = 0;  // 非驾驶员
            personInfo.tIsForeigner.bHas = true;
            personInfo.tIsForeigner.value = 0;  // 非涉外人员
            personInfo.tIsSuspectedTerrorist.bHas = true;
            personInfo.tIsSuspectedTerrorist.value = 2;  // 非涉恐人员(2表示未知)
            personInfo.tIsCriminalInvolved.bHas = true;
            personInfo.tIsCriminalInvolved.value = 2;  // 非涉案人员
            personInfo.tIsDetainees.bHas = true;
            personInfo.tIsDetainees.value = 2;  // 非在押人员
            personInfo.tIsSuspiciousPerson.bHas = true;
            personInfo.tIsSuspiciousPerson.value = 2;  // 非可疑人员
            personInfo.tIsVictim.bHas = true;
            personInfo.tIsVictim.value = 2;  // 非受害人

            // 人员属性
            personInfo.tAgeLowerLimit.bHas = true;
            personInfo.tAgeLowerLimit.value = 0;  // 年龄下限
            personInfo.tAgeUpLimit.bHas = true;
            personInfo.tAgeUpLimit.value = 0;  // 年龄上限

            personInfo.tHeightLowerLimit.bHas = true;
            personInfo.tHeightLowerLimit.value = 0;  // 身高下限
            personInfo.tHeightUpLimit.bHas = true;
            personInfo.tHeightUpLimit.value = 0;  // 身高上限

            // 性别代码: 1-男性, 2-女性, 9-未知
            strncpy(personInfo.achGenderCode, "9", sizeof(personInfo.achGenderCode) - 1);  // 默认未知
            personInfo.achGenderCode[sizeof(personInfo.achGenderCode) - 1] = '\0';

            // 陪同人数
            personInfo.tAccompanyNumber.bHas = true;
            personInfo.tAccompanyNumber.value = 0;

            // 人员子图像设置
            personInfo.nSubImageNum = 1;
            TPubSecSubImageList* tPersonSubImg = new TPubSecSubImageList();
            memset(tPersonSubImg, 0, sizeof(TPubSecSubImageList));
            personInfo.ptPersonSubImage = tPersonSubImg;

            // 设置子图像信息
            strncpy(tPersonSubImg->achImageID, imageInfo.achImageID, PUBSEC_BasicObjectIdType - 1);
            tPersonSubImg->achImageID[PUBSEC_BasicObjectIdType - 1] = '\0';
            strncpy(tPersonSubImg->achDeviceID, m_pubsecCommonInfo[0].achDeviceID, sizeof(tPersonSubImg->achDeviceID) - 1);
            tPersonSubImg->achDeviceID[sizeof(tPersonSubImg->achDeviceID) - 1] = '\0';
            strncpy(tPersonSubImg->achStoragePath, "http://", sizeof(tPersonSubImg->achStoragePath) - 1);
            tPersonSubImg->achStoragePath[sizeof(tPersonSubImg->achStoragePath) - 1] = '\0';
            strncpy(tPersonSubImg->achType, "11", sizeof(tPersonSubImg->achType) - 1);  // 人脸图
            tPersonSubImg->achType[sizeof(tPersonSubImg->achType) - 1] = '\0';
            strncpy(tPersonSubImg->achFileFormat, imageInfo.achFileFormat, sizeof(tPersonSubImg->achFileFormat) - 1);
            tPersonSubImg->achFileFormat[sizeof(tPersonSubImg->achFileFormat) - 1] = '\0';
            strncpy(tPersonSubImg->achShotTime, timeStr, sizeof(tPersonSubImg->achShotTime) - 1);
            tPersonSubImg->achShotTime[sizeof(tPersonSubImg->achShotTime) - 1] = '\0';
            tPersonSubImg->nWidth = imageInfo.nWidth;
            tPersonSubImg->nHeight = imageInfo.nHeight;

            // 设置图片路径
            if (!spotImgpath.empty()) {
                strncpy(tPersonSubImg->achFilePath, spotImgpath.c_str(), sizeof(tPersonSubImg->achFilePath) - 1);
                tPersonSubImg->achFilePath[sizeof(tPersonSubImg->achFilePath) - 1] = '\0';
            } else if (!cropImgpath.empty()) {
                strncpy(tPersonSubImg->achFilePath, cropImgpath.c_str(), sizeof(tPersonSubImg->achFilePath) - 1);
                tPersonSubImg->achFilePath[sizeof(tPersonSubImg->achFilePath) - 1] = '\0';
            }

            // 门禁记录信息填充
            TPubSecAccessRecord& accessRecord = ptImageGather->ptWisComAcceRec[0];
            memset(&accessRecord, 0, sizeof(accessRecord));
            
            // 设置门禁记录信息
            strncpy(accessRecord.achAccessRecordID, imageInfo.achImageID, sizeof(accessRecord.achAccessRecordID) - 1);
            accessRecord.achAccessRecordID[sizeof(accessRecord.achAccessRecordID) - 1] = '\0';
            strncpy(accessRecord.achSourceID, imageInfo.achImageID, sizeof(accessRecord.achSourceID) - 1);
            accessRecord.achSourceID[sizeof(accessRecord.achSourceID) - 1] = '\0';
            strncpy(accessRecord.achDeviceID, m_pubsecCommonInfo[0].achDeviceID, sizeof(accessRecord.achDeviceID) - 1);
            accessRecord.achDeviceID[sizeof(accessRecord.achDeviceID) - 1] = '\0';
            
            // SecureAccessID - 根据记录生成
            snprintf(accessRecord.achSecureAccessID, sizeof(accessRecord.achSecureAccessID), "secure_%s", record["recordId"].asString().c_str());
            
            // 报警事件代码 - 默认设置为门禁事件
            int32_t failType = record["failType"].asInt();
            Json::Value details = Uface::WebApp::getRecordDetails(record["elemType"].asInt64());
            int32_t iPermission = AlgReactor::detectSuccess;
            if (details["permission"].asInt()==AlgReactor::detectFailed 
                || details["access"].asInt()==AlgReactor::detectFailed ) {
                //是否在passtime内
                iPermission = AlgReactor::detectFailed;
            }

            infof("result failType : %d  detail : %s \n", failType, details.toStyledString().c_str());
            if (failType == AlgReactor::aliveDetectFailed) {
                iPermission = AlgReactor::detectFailed;
            }

            //部分类型平台不支持，暂不处理
            Json::Value data = Json::nullValue;
            int32_t spotType = record["spotType"].asInt();
            switch (spotType) {
                case IdentifySpotType::faceSpotType : {
                    data[IMAGETYPE] = "FACE"; //图片类型
                    if (details["compare"].asInt() == AlgReactor::detectFailed) {
                        data[EVENTCODE] = FACE_FAIL_EVNET;
                    } else {
                        data[EVENTCODE] = iPermission == AlgReactor::detectFailed? FACE_FAIL_EVNET : FACE_SUCCESS_EVNET;
                    }
                    break;
                }
                case faceCardSpotType: {
                    data[IMAGETYPE] = "FACE"; //图片类型
                    if (details["compare"].asInt() == AlgReactor::detectFailed) {
                        data[EVENTCODE] = FACE_CARD_FAIL_EVNET;
                    } else {
                        data[EVENTCODE] = iPermission == AlgReactor::detectFailed? FACE_CARD_FAIL_EVNET:FACE_CARD_SUCCESS_EVNET;
                    }
                    break;
                }
                case facePersonIdSpotType: {
                    data[IMAGETYPE] = "FACE"; //图片类型
                    if (details["compare"].asInt() == AlgReactor::detectFailed) {
                        data[EVENTCODE] = FACE_IDCARD_FAIL_EVNET;
                    } else {
                        data[EVENTCODE] = iPermission == AlgReactor::detectFailed? FACE_IDCARD_FAIL_EVNET:FACE_IDCARD_SUCCESS_EVNET;
                    }
                    break;
                }
                case cardAlgSpotType: {
                    if (details["compare"].asInt() == AlgReactor::detectFailed) {
                        data[EVENTCODE] = CARD_FAIL_EVNET;
                    } else {
                        data[EVENTCODE] = iPermission == AlgReactor::detectFailed ? CARD_FAIL_EVNET : CARD_SUCCESS_EVNET;
                    }
                    break;
                }
                case openPasswordSpotType: {
                    if (details["compare"].asInt() == AlgReactor::detectFailed) {
                        data[EVENTCODE] = PASSWD_FAIL_EVNET;
                    } else {
                        data[EVENTCODE] = iPermission == AlgReactor::detectFailed ? PASSWD_FAIL_EVNET : PASSWD_SUCCESS_EVNET;
                    }
                    break;
                }
                case fingerSpotType: {
                    if (details["compare"].asInt() == AlgReactor::detectFailed) {
                        data[EVENTCODE] = FINGER_FAIL_EVNET;
                    } else {
                        data[EVENTCODE] = iPermission == AlgReactor::detectFailed ? FINGER_FAIL_EVNET : FINGER_SUCCESS_EVNET;
                    }
                    break;
                }
                case personIdSpotType: {
                    if (details["compare"].asInt() == AlgReactor::detectFailed) {
                        data[EVENTCODE] = IDCARD_FAIL_EVNET;
                    } else {
                        data[EVENTCODE] = iPermission == AlgReactor::detectFailed ? IDCARD_FAIL_EVNET : IDCARD_SUCCESS_EVNET;
                    }
                    break;
                }
                case openRemoteSpotType: {
                    data[EVENTCODE] = OTHER_OPEN_EVNET;
                    break;
                }
                case qrCodeSpotType: {
                    if (details["compare"].asInt() == AlgReactor::detectFailed) {
                        data[EVENTCODE] = QRCODE_FAIL_EVNET;
                    } else {
                        data[EVENTCODE] = iPermission == AlgReactor::detectFailed ? QRCODE_FAIL_EVNET : QRCODE_SUCCESS_EVNET;
                    }
                    break;
                }
                case openButtonSpotType: {
                    data[EVENTCODE] = OPENDOOR_ALARM_EVENT;   //信号开门成功
                    break;
                }
                case 10303: {
                    data[EVENTCODE] = FIRE_ALARM_EVENT;   //火警触发开门
                    break;
                }

                case 10304: {
                    data[EVENTCODE] = ANTI_THEFT_EVENT;   //门禁防盗事件
                    break;
                }

                default:
                    data[EVENTCODE] = "";
                    break;
            }

            //特殊的失败类型解析-> 黑名单失败类型
            if( failType == 8 ) {
                data[EVENTCODE] = BLACKLIST_FAIL_EVNET;
            }

            strncpy(accessRecord.achAlarmEventCode, data[EVENTCODE].asString().c_str(), sizeof(accessRecord.achAlarmEventCode) - 1);
            accessRecord.achAlarmEventCode[sizeof(accessRecord.achAlarmEventCode) - 1] = '\0';
            infof("accessRecord.achAlarmEventCode : %s \n", accessRecord.achAlarmEventCode);
            
            // 卡号 - 从spotContent中获取cardNo
            accessRecord.dwCardID = 0;
            if (record.isMember("spotContent") && record["spotContent"].isMember("cardNo")) {
                try {
                    accessRecord.dwCardID = std::stoul(record["spotContent"]["cardNo"].asString());
                } catch (...) {
                    // 转换失败时保持默认值0
                }
            }
            
            // 卡类型 - 默认为1
            accessRecord.nCardType = 1;
            
            // 人员编码 - 使用personId
            if (record.isMember("personId")) {
                strncpy(accessRecord.achPersonCode, record["personId"].asString().c_str(), sizeof(accessRecord.achPersonCode) - 1);
                accessRecord.achPersonCode[sizeof(accessRecord.achPersonCode) - 1] = '\0';
            } else {
                memset(accessRecord.achPersonCode, 0, sizeof(accessRecord.achPersonCode));
            }
            
            // 门禁时间
            strncpy(accessRecord.achAccessTime, imageInfo.achShotTime, sizeof(accessRecord.achAccessTime) - 1);
            accessRecord.achAccessTime[sizeof(accessRecord.achAccessTime) - 1] = '\0';
            
            // 进入时间
            strncpy(accessRecord.achEntryTime, imageInfo.achShotTime, sizeof(accessRecord.achEntryTime) - 1);
            accessRecord.achEntryTime[sizeof(accessRecord.achEntryTime) - 1] = '\0';
            
            // 打卡时间
            strncpy(accessRecord.achPunchTime, imageInfo.achShotTime, sizeof(accessRecord.achPunchTime) - 1);
            accessRecord.achPunchTime[sizeof(accessRecord.achPunchTime) - 1] = '\0';
            
            // 打卡动作 - 默认为1(进入)
            accessRecord.nPunchAct = 1;
            
            // 打卡结果 - 根据识别结果设置
            if (record.isMember("result")) {
                // 假设0表示成功，非0表示失败
                accessRecord.nPunchResult = (record["result"].asInt() == 0) ? 1 : 0;
            } else {
                accessRecord.nPunchResult = 1;  // 默认成功
            }
            
            // 从spotContent中获取更多信息
            if (record.isMember("spotContent")) {
                const Json::Value& spotContent = record["spotContent"];
                
                // 证件号码
                if (spotContent.isMember("idNumber")) {
                    strncpy(personInfo.achIDNumber, spotContent["idNumber"].asString().c_str(), sizeof(personInfo.achIDNumber) - 1);
                    personInfo.achIDNumber[sizeof(personInfo.achIDNumber) - 1] = '\0';
                }
                
                // 证件类型
                if (spotContent.isMember("identityType")) {
                    if (spotContent["identityType"].asString() == "ID_CARD") {
                        strncpy(personInfo.achIDType, "111", sizeof(personInfo.achIDType) - 1); // 居民身份证
                        personInfo.achIDType[sizeof(personInfo.achIDType) - 1] = '\0';
                    } else {
                        strncpy(personInfo.achIDType, "114", sizeof(personInfo.achIDType) - 1); // 其他证件类型
                        personInfo.achIDType[sizeof(personInfo.achIDType) - 1] = '\0';
                    }
                }
                
                // 图像文件路径
                if (spotContent.isMember("temp") && spotContent["temp"].isMember("spotImage")) {
                    strncpy(imageInfo.achFilePath, spotContent["temp"]["spotImage"].asString().c_str(), sizeof(imageInfo.achFilePath) - 1);
                    imageInfo.achFilePath[sizeof(imageInfo.achFilePath) - 1] = '\0';
                }
                
            }
        }

        // telnet命令处理函数实现
        std::string Pubsec::pubsecCommand(const std::vector<std::string>& args)
        {
            if (args.empty()) {
                // 显示帮助信息
                return "Pubsec Commands:\r\n"
                       "  pubsec status - Show pubsec registration status\r\n"
                       "  pubsec info - Show pubsec configuration info\r\n"
                       "  pubsec init - Initialize pubsec module\r\n"
                       "  pubsec deinit - Deinitialize pubsec module\r\n"
                       "  pubsec register - Force register to platform\r\n"
                       "  pubsec unregister - Force unregister from platform\r\n"
                       "  pubsec person - Send test person image\r\n"
                       "  pubsec img - Send test image gather\r\n"
                       "  pubsec snap - Take snapshot and send\r\n";
            }

            std::string cmd = args[0];

            if (cmd == "status") {
                std::string result = "Pubsec Status:\r\n";
                result += "  Initialized: " + std::string(m_initialized ? "Yes" : "No") + "\r\n";
                result += "  Registered: " + std::string(m_registered ? "Yes" : "No") + "\r\n";
                result += "  Register Thread Running: " + std::string(m_registerThreadRunning ? "Yes" : "No") + "\r\n";

                if (m_initialized) {
                    result += "  Device ID: " + std::string(m_pubsecCommonInfo[0].achDeviceID) + "\r\n";
                    result += "  Server: " + std::string(m_pubsecCommonInfo[0].tServerIPAddress.achIPV4) +
                             ":" + std::to_string(m_pubsecCommonInfo[0].tServerIPAddress.nPort) + "\r\n";
                    result += "  Username: " + std::string(m_pubsecCommonInfo[0].tUserInfo.achUser) + "\r\n";
                    result += "  Enabled: " + std::string(m_pubsecCommonInfo[0].bEnable ? "Yes" : "No") + "\r\n";
                }
                return result;
            }
            else if (cmd == "info") {
                if (!m_initialized) {
                    return "Pubsec not initialized. Use 'pubsec init' first.\r\n";
                }

                std::string result = "Pubsec Configuration:\r\n";
                result += "  Device ID: " + std::string(m_pubsecCommonInfo[0].achDeviceID) + "\r\n";
                result += "  Server Address: " + std::string(m_pubsecCommonInfo[0].tServerIPAddress.achIPV4) + "\r\n";
                result += "  Server Port: " + std::to_string(m_pubsecCommonInfo[0].tServerIPAddress.nPort) + "\r\n";
                result += "  Username: " + std::string(m_pubsecCommonInfo[0].tUserInfo.achUser) + "\r\n";
                result += "  Password: " + std::string(m_pubsecCommonInfo[0].tUserInfo.achKey) + "\r\n";
                result += "  Enabled: " + std::string(m_pubsecCommonInfo[0].bEnable ? "Yes" : "No") + "\r\n";
                result += "  Heartbeat Interval: " + std::to_string(m_pubsecCommonInfo[0].nKeepAliveTime) + " seconds\r\n";
                result += "  Sync Interval: " + std::to_string(m_pubsecCommonInfo[0].dwSyncTimeInterval) + " seconds\r\n";
                return result;
            }
            else if (cmd == "init") {
                if (m_initialized) {
                    return "Pubsec already initialized.\r\n";
                }

                bool result = initial();
                return result ? "Pubsec initialized successfully.\r\n" : "Failed to initialize pubsec.\r\n";
            }
            else if (cmd == "deinit") {
                if (!m_initialized) {
                    return "Pubsec not initialized.\r\n";
                }

                deinitial();
                return "Pubsec deinitialized successfully.\r\n";
            }
            else if (cmd == "register") {
                if (!m_initialized) {
                    return "Pubsec not initialized. Use 'pubsec init' first.\r\n";
                }

                // 触发重新注册
                {
                    std::lock_guard<std::mutex> lock(m_registerMutex);
                    m_cfgChange = true;
                    m_registerCV.notify_one();
                }
                return "Register request sent. Check status with 'pubsec status'.\r\n";
            }
            else if (cmd == "unregister") {
                if (!m_initialized) {
                    return "Pubsec not initialized.\r\n";
                }

                // 触发注销
                {
                    std::lock_guard<std::mutex> lock(m_registerMutex);
                    m_registered = false;
                    m_cfgChange = true;
                    m_registerCV.notify_one();
                }
                return "Unregister request sent.\r\n";
            }
            else if (cmd == "person") {
                if (!m_registered) {
                    return "Pubsec not registered to platform. Cannot send person data.\r\n";
                }

                try {
                    postimagePerson();
                    return "Test person image sent successfully.\r\n";
                } catch (const std::exception& e) {
                    return "Failed to send person image: " + std::string(e.what()) + "\r\n";
                }
            }
            else if (cmd == "img") {
                if (!m_registered) {
                    return "Pubsec not registered to platform. Cannot send image data.\r\n";
                }

                try {
                    Json::Value dummyRecord;
                    dummyRecord["recordId"] = "test_" + std::to_string(time(nullptr));
                    dummyRecord["createdAt"] = UBase::CTime::getCurrentMilliSecond();
                    postimageGather(dummyRecord);
                    return "Test image gather sent successfully.\r\n";
                } catch (const std::exception& e) {
                    return "Failed to send image gather: " + std::string(e.what()) + "\r\n";
                }
            }
            else if (cmd == "snap") {
                if (!m_registered) {
                    return "Pubsec not registered to platform. Cannot send snapshot.\r\n";
                }

                try {
                    std::string imagePath = snapImage();
                    if (imagePath.empty()) {
                        return "Failed to take snapshot.\r\n";
                    }

                    // 创建包含图片路径的记录
                    Json::Value record;
                    record["recordId"] = "snap_" + std::to_string(time(nullptr));
                    record["createdAt"] = UBase::CTime::getCurrentMilliSecond();

                    Json::Value spotContent;
                    Json::Value temp;
                    temp["spotImage"] = imagePath;
                    spotContent["temp"] = temp;
                    record["spotContent"] = spotContent;

                    postimageGather(record);
                    return "Snapshot taken and sent successfully: " + imagePath + "\r\n";
                } catch (const std::exception& e) {
                    return "Failed to take and send snapshot: " + std::string(e.what()) + "\r\n";
                }
            }
            else {
                return "Unknown pubsec command: " + cmd + "\r\n"
                       "Use 'pubsec' without arguments to see available commands.\r\n";
            }
        }

        // 从JSON解析事件到RAII管理器（新版本）
        void Pubsec::jsonToEventRAII(const Json::Value& record, ImageGatherManager& gatherManager)
        {
            if (record.empty()) {
                errorf("Invalid parameters\n");
                return;
            }

            if (!record.isMember("recordId") || !record["recordId"].isString()) {
                errorf("Invalid recordId format\n");
                return;
            }

            std::string spotImgpath;
            std::string cropImgpath;

            // 获取图像信息
            TPubSecImageObjInfo& imageInfo = gatherManager.getImage(0);

            char timeStr[32];
            if(record.isMember("createdAt")) {
                uint64_t eventTime = record["createdAt"].asUInt64();

                UBase::CTime cur = UBase::CTime(eventTime);
                char dateBuf[64] = {0};
                cur.format(dateBuf,"yyyyMMdd", UBase::CTime::fmDateFormat);
                infof("eventTimeStr datepart = %s\n", dateBuf);

                char timeBuf[64] = {0};
                cur.format(timeBuf,"HHmmss", UBase::CTime::fmHourFormat);
                infof("eventTimeStr timepart = %s\n", timeBuf);

                // 合并日期和时间，格式为yyyyMMddHHmmss
                SafeStringUtils::safeFormat(timeStr, "%s%s", dateBuf, timeBuf);
                infof("timeStr = %s\n", timeStr);
            }
            else {
                // 获取当前时间
                time_t now = time(0);
                struct tm *ltm = localtime(&now);

                strftime(timeStr, sizeof(timeStr), "%Y%m%d%H%M%S", ltm);
                infof("timeStr = %s\n", timeStr);
            }

            // 将recordId从字符串转换为整数
            int recordId = std::stoi(record["recordId"].asString());

            // 设置图像基本信息 - 使用SafeStringUtils进行安全字符串操作
            SafeStringUtils::safeFormat(imageInfo.achImageID, "%s%s%05d",
                m_pubsecCommonInfo[0].achDeviceID, timeStr, recordId);
            imageInfo.nInfoKind = PUBSEC_INTELLI_MAN_TYPE_MANUAL;  // 使用枚举类型
            SafeStringUtils::safeCopy(imageInfo.achImageSource, "17");  // ImageSource: "17"
            SafeStringUtils::safeCopy(imageInfo.achDeviceID, m_pubsecCommonInfo[0].achDeviceID);
            SafeStringUtils::safeCopy(imageInfo.achStoragePath, "");  // StoragePath: ""
            SafeStringUtils::safeCopy(imageInfo.achFileHash, "");  // FileHash: ""
            SafeStringUtils::safeCopy(imageInfo.achFileFormat, "Jpeg");  // FileFormat: "Jpeg"

            // 设置时间
            SafeStringUtils::safeCopy(imageInfo.achShotTime, timeStr);

            // 设置图像尺寸
            imageInfo.nWidth = 176;  // Width: 176
            imageInfo.nHeight = 202;  // Height: 202

            // 题名
            SafeStringUtils::safeCopy(imageInfo.achTitle, "门禁记录");

            // ContentDescription
            SafeStringUtils::safeCopy(imageInfo.achContentDescription, "三代门禁-人脸");

            // ShotPlaceFullAdress
            SafeStringUtils::safeCopy(imageInfo.achShotPlaceFullAdress, "门禁位置");
            SafeStringUtils::safeCopy(imageInfo.achSecurityLevel, "5");  // SecurityLevel: "5"

            // 处理图像文件路径
            if (record.isMember("spotContent")) {
                const Json::Value& spotContent = record["spotContent"];

                // 图像文件路径
                if (spotContent.isMember("temp") && spotContent["temp"].isMember("spotImage")) {
                    SafeStringUtils::safeCopy(imageInfo.achFilePath, spotContent["temp"]["spotImage"].asString());
                    spotImgpath = spotContent["temp"]["spotImage"].asString();
                    infof("spotImgpath = %s\n", spotImgpath.c_str());
                }

                if (spotContent.isMember("temp") && spotContent["temp"].isMember("cropImage")) {
                    SafeStringUtils::safeCopy(imageInfo.achFilePath, spotContent["temp"]["cropImage"].asString());
                    cropImgpath = spotContent["temp"]["cropImage"].asString();
                    infof("cropImgpath = %s\n", cropImgpath.c_str());
                }
            }

            // 初始化人员信息
            TPubSecPersonnelObjInfo& personInfo = gatherManager.getPersonnel(0);

            // 人员标识
            SafeStringUtils::safeCopy(personInfo.achPersonID, imageInfo.achImageID);

            // 信息分类
            personInfo.nInfoKind = imageInfo.nInfoKind;

            // 来源图像信息标识
            SafeStringUtils::safeCopy(personInfo.achSourceID, imageInfo.achImageID);

            // 设备编码
            SafeStringUtils::safeCopy(personInfo.achDeviceID, m_pubsecCommonInfo[0].achDeviceID);

            // 坐标信息（必选，但可以为0）
            personInfo.tLeftTopX.bHas = true;
            personInfo.tLeftTopX.value = 0;
            personInfo.tLeftTopY.bHas = true;
            personInfo.tLeftTopY.value = 0;
            personInfo.tRightBtmX.bHas = true;
            personInfo.tRightBtmX.value = imageInfo.nWidth;
            personInfo.tRightBtmY.bHas = true;
            personInfo.tRightBtmY.value = imageInfo.nHeight;

            // 设置人员子图像 - 使用RAII管理器分配
            SubImageListManager* subImageManager = gatherManager.allocateSubImages(0, 1);
            if (subImageManager) {
                TPubSecSubImageList& tPersonSubImg = (*subImageManager)[0];

                // 设置子图像信息
                SafeStringUtils::safeCopy(tPersonSubImg.achImageID, imageInfo.achImageID);
                SafeStringUtils::safeCopy(tPersonSubImg.achDeviceID, m_pubsecCommonInfo[0].achDeviceID);
                SafeStringUtils::safeCopy(tPersonSubImg.achStoragePath, "http://");  // StoragePath: "http://"
                SafeStringUtils::safeCopy(tPersonSubImg.achType, "11");  // Type: "11"
                SafeStringUtils::safeCopy(tPersonSubImg.achFileFormat, "Jpeg");  // FileFormat: "Jpeg"
                SafeStringUtils::safeCopy(tPersonSubImg.achShotTime, timeStr);
                tPersonSubImg.nWidth = 176;  // Width: 176
                tPersonSubImg.nHeight = 202;  // Height: 202
                SafeStringUtils::safeCopy(tPersonSubImg.achFilePath, spotImgpath);  // 保持图片路径不变
            }

            // 初始化门禁记录
            TPubSecAccessRecord& accessRecord = gatherManager.getAccessRecord(0);

            // 设置门禁记录信息 - 使用SafeStringUtils进行安全字符串操作
            SafeStringUtils::safeCopy(accessRecord.achAccessRecordID, imageInfo.achImageID);
            SafeStringUtils::safeCopy(accessRecord.achSourceID, imageInfo.achImageID);
            SafeStringUtils::safeCopy(accessRecord.achDeviceID, m_pubsecCommonInfo[0].achDeviceID);
            SafeStringUtils::safeCopy(accessRecord.achSecureAccessID, "secure_test");
            SafeStringUtils::safeCopy(accessRecord.achAlarmEventCode, "00-01");  // AlarmEventCode: "00-01"
            accessRecord.dwCardID = 0;  // CardID: 0
            accessRecord.nCardType = 1;  // CardType: 1
            SafeStringUtils::safeCopy(accessRecord.achPersonCode, "3424");  // PersonCode: "3424"
            SafeStringUtils::safeCopy(accessRecord.achAccessTime, timeStr);  // AccessTime
            SafeStringUtils::safeCopy(accessRecord.achEntryTime, timeStr);  // EntryTime
            SafeStringUtils::safeCopy(accessRecord.achPunchTime, timeStr);  // PunchTime
            accessRecord.nPunchAct = 1;  // PunchAct: 1
            accessRecord.nPunchResult = 1;  // PunchResult: 1
        }

    }
}