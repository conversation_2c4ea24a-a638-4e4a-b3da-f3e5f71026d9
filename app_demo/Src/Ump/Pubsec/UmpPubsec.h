/************************************************
 * Copyright(c) 2020 ump
 *
 * Project:    Pubsec
 * FileName:   Pubsec.h
 * Author:
 * Email:
 * Version:    V1.0.0
 * Date:       2023-07-20
 * Description: 公安视图库初始化类
 * Others:
 *************************************************/

#ifndef _INCLUDE_PUBSEC_H_
#define _INCLUDE_PUBSEC_H_
#pragma once

#include <thread>
#include <mutex>
#include <atomic>
#include <string>
#include <vector>
#include <map>
#include <condition_variable>
#include <chrono>

#include "PubsecDefine.h"
#include "PubsecRAII.h"
#include "AppEvent/Define.h"
#include "UBase/Timer/Timer.h"
#include "UBase/Buffer/Queue.h"
#include "UBase/Thread/ThreadLite.h"
#include "Traffic/Data/DataAction.h"
#include "Object/Factory.h"
#include "Json/Value.h"
#include "Ump/UmpCore.h"

namespace Uface
{
    namespace Application
    {

        class Pubsec
        {
        public:
            // 获取单例实例（修改为返回引用）
            static Pubsec* instance();

            // 初始化视图库
            bool initial();

            // 反初始化视图库
            void deinitial();

            // 获取初始化状态
            bool isInitialized() const;

            // 获取视图库注册状态
            bool getRegisterStatus() const;

            // 发送人员图像信息
            void postimagePerson();
            
            // 发送图像采集信息
            void postimageGather(const Json::Value &record);
            
            // 停止所有线程
            void stopAllThreads();

            // 平台配置回调方法
            void onConfig(const char *name, const char *client, const Json::Value &config);

            // 报警回调方法
            void onAlarm(AppEvent::EventSubType type, const Json::Value &data);

            // 注册识别回调方法
            bool attachRecordCallback();

            // 注销识别回调方法
            bool detachRecordCallback();

            // 识别回调方法
            bool onRecord(const Json::Value &record);

            // 数据回调方法
            void onData(const Json::Value &data);

            // telnet命令处理函数
            std::string pubsecCommand(const std::vector<std::string>& args);

        private:
            // 私有构造函数和析构函数，防止外部创建实例
            Pubsec();
            ~Pubsec();

            // 禁用拷贝构造和赋值操作
            Pubsec(const Pubsec &) = delete;
            Pubsec &operator=(const Pubsec &) = delete;

            // 从JSON解析参数到TPubSecCommonObjInfo结构体
            void jsonToParam(const Json::Value& pubsecCfg, TPubSecCommonObjInfo* pstCommonObjInfo);

            // 从JSON解析事件到TPubSecObjList结构体
            void jsonToEvent(const Json::Value& record, TPubSecObjList* ptImageGather);

            // 从JSON解析事件到RAII管理器（新版本）
            void jsonToEventRAII(const Json::Value& record, ImageGatherManager& gatherManager);

            // 心跳管理器
            std::unique_ptr<HeartbeatManager> heartbeatManager_;

            // 初始化状态
            std::atomic<bool> m_initialized{false};
            // 视图库注册成功标志
            std::atomic<bool> m_registered{false};
            // 视图库参数变化标志
            std::atomic<bool> m_cfgChange{false};

            // 配置信息
            std::mutex m_configMutex;
            static std::map<std::string, std::string> m_configMap;

            // 数据操作指针
            Data::DataActionPtr mDataActionPtr;

            // 公安视图库参数
            TPubSecCommonObjInfo m_pubsecCommonInfo[1];
            TPubSecCommonObjInfo m_pubsecCommonInfoNew[1];

            // 内部方法
            bool initialStack();

            // 创建并启动视图库注册线程
            void createRegisterThread();
            // 停止视图库注册线程
            void stopRegisterThread();

            // 线程控制
            std::atomic<bool> m_registerThreadRunning{false};
            std::thread m_registerThread;

            // 注册线程同步相关
            std::mutex m_registerMutex;
            std::condition_variable m_registerCV;

            // 心跳线程相关（保留兼容性）
            std::atomic<bool> m_heartbeatThreadRunning{false};
            std::thread m_heartbeatThread;
            std::mutex m_heartbeatMutex;
            std::condition_variable m_heartbeatCV;
            uint32_t m_heartbeatInterval;  // 心跳间隔时间（秒）

        };

    }
}
#endif //_INCLUDE_PUBSEC_H_