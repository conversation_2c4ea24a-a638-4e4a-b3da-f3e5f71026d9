/**
 * @file     test_pubsec_raii.cpp
 * @brief    公安视图库RAII类单元测试
 * <AUTHOR> @date     2024-01-20
 * @version  1.0
 */

#include <iostream>
#include <cassert>
#include <cstring>
#include "../../Src/Ump/Pubsec/PubsecRAII.h"

using namespace Uface::Application;

// 简单的测试框架
class SimpleTest {
private:
    static int totalTests;
    static int passedTests;

public:
    static void assert_true(bool condition, const std::string& testName) {
        totalTests++;
        if (condition) {
            passedTests++;
            std::cout << "[PASS] " << testName << std::endl;
        } else {
            std::cout << "[FAIL] " << testName << std::endl;
        }
    }

    static void assert_equal(const std::string& expected, const std::string& actual, const std::string& testName) {
        assert_true(expected == actual, testName);
    }

    static void printSummary() {
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "Total tests: " << totalTests << std::endl;
        std::cout << "Passed: " << passedTests << std::endl;
        std::cout << "Failed: " << (totalTests - passedTests) << std::endl;
        std::cout << "Success rate: " << (passedTests * 100.0 / totalTests) << "%" << std::endl;
    }
};

int SimpleTest::totalTests = 0;
int SimpleTest::passedTests = 0;

// 测试SafeStringUtils类
void testSafeStringUtils() {
    std::cout << "\n=== Testing SafeStringUtils ===" << std::endl;

    // 测试safeCopy with std::string
    {
        char buffer[10];
        std::string source = "Hello";
        SafeStringUtils::safeCopy(buffer, source);
        SimpleTest::assert_equal("Hello", std::string(buffer), "SafeCopy with string - normal case");
    }

    // 测试safeCopy with overflow protection
    {
        char buffer[5];
        std::string source = "HelloWorld";
        SafeStringUtils::safeCopy(buffer, source);
        SimpleTest::assert_true(strlen(buffer) == 4, "SafeCopy with string - overflow protection");
        SimpleTest::assert_true(buffer[4] == '\0', "SafeCopy with string - null termination");
    }

    // 测试safeCopy with C string
    {
        char buffer[10];
        const char* source = "Test";
        SafeStringUtils::safeCopy(buffer, source);
        SimpleTest::assert_equal("Test", std::string(buffer), "SafeCopy with C string - normal case");
    }

    // 测试safeCopy with null pointer
    {
        char buffer[10];
        SafeStringUtils::safeCopy(buffer, static_cast<const char*>(nullptr));
        SimpleTest::assert_true(buffer[0] == '\0', "SafeCopy with null pointer");
    }

    // 测试safeFormat
    {
        char buffer[20];
        SafeStringUtils::safeFormat(buffer, "Number: %d", 42);
        SimpleTest::assert_equal("Number: 42", std::string(buffer), "SafeFormat - normal case");
    }
}

// 测试SubImageListManager类
void testSubImageListManager() {
    std::cout << "\n=== Testing SubImageListManager ===" << std::endl;

    // 测试构造和基本操作
    {
        SubImageListManager manager(3);
        SimpleTest::assert_true(manager.size() == 3, "SubImageListManager - size check");
        SimpleTest::assert_true(manager.get() != nullptr, "SubImageListManager - get pointer not null");
    }

    // 测试索引访问
    {
        SubImageListManager manager(2);
        TPubSecSubImageList& first = manager[0];
        TPubSecSubImageList& second = manager[1];

        // 设置一些值来验证不同的对象
        SafeStringUtils::safeCopy(first.achImageID, "image1");
        SafeStringUtils::safeCopy(second.achImageID, "image2");

        SimpleTest::assert_equal("image1", std::string(first.achImageID), "SubImageListManager - first element access");
        SimpleTest::assert_equal("image2", std::string(second.achImageID), "SubImageListManager - second element access");
    }

    // 测试越界检查
    {
        SubImageListManager manager(1);
        bool exceptionThrown = false;
        try {
            manager[1]; // 应该抛出异常
        } catch (const PubsecInvalidParameterException& e) {
            exceptionThrown = true;
        }
        SimpleTest::assert_true(exceptionThrown, "SubImageListManager - out of bounds exception");
    }
}

// 测试ImageGatherManager类
void testImageGatherManager() {
    std::cout << "\n=== Testing ImageGatherManager ===" << std::endl;

    // 测试构造
    {
        ImageGatherManager manager(1, 1, 1, 1);
        SimpleTest::assert_true(manager.getImageCount() == 1, "ImageGatherManager - image count");
        SimpleTest::assert_true(manager.getPersonnelCount() == 1, "ImageGatherManager - personnel count");
        SimpleTest::assert_true(manager.getFaceCount() == 1, "ImageGatherManager - face count");
        SimpleTest::assert_true(manager.getAccessRecordCount() == 1, "ImageGatherManager - access record count");
    }

    // 测试对象访问
    {
        ImageGatherManager manager(1, 1, 1, 1);

        TPubSecImageObjInfo& image = manager.getImage(0);
        TPubSecPersonnelObjInfo& personnel = manager.getPersonnel(0);
        TPubSecFaceObjInfo& face = manager.getFace(0);
        TPubSecAccessRecord& record = manager.getAccessRecord(0);

        // 设置一些值来验证对象可用
        SafeStringUtils::safeCopy(image.achImageID, "img001");
        SafeStringUtils::safeCopy(personnel.achPersonID, "person001");
        SafeStringUtils::safeCopy(face.achsFaceID, "face001");
        SafeStringUtils::safeCopy(record.achAccessRecordID, "record001");

        SimpleTest::assert_equal("img001", std::string(image.achImageID), "ImageGatherManager - image object access");
        SimpleTest::assert_equal("person001", std::string(personnel.achPersonID), "ImageGatherManager - personnel object access");
        SimpleTest::assert_equal("face001", std::string(face.achsFaceID), "ImageGatherManager - face object access");
        SimpleTest::assert_equal("record001", std::string(record.achAccessRecordID), "ImageGatherManager - record object access");
    }

    // 测试子图像分配
    {
        ImageGatherManager manager(0, 1, 0, 0);
        SubImageListManager* subManager = manager.allocateSubImages(0, 2);

        SimpleTest::assert_true(subManager != nullptr, "ImageGatherManager - sub image allocation");
        SimpleTest::assert_true(subManager->size() == 2, "ImageGatherManager - sub image count");

        // 验证人员对象的子图像指针已设置
        TPubSecPersonnelObjInfo& personnel = manager.getPersonnel(0);
        SimpleTest::assert_true(personnel.ptPersonSubImage != nullptr, "ImageGatherManager - personnel sub image pointer set");
        SimpleTest::assert_true(personnel.nSubImageNum == 2, "ImageGatherManager - personnel sub image count set");
    }

    // 测试toObjList
    {
        ImageGatherManager manager(1, 1, 1, 1);
        TPubSecObjList objList = manager.toObjList();

        SimpleTest::assert_true(objList.ptPubSecImageList != nullptr, "ImageGatherManager - toObjList image list");
        SimpleTest::assert_true(objList.ptPersonnelObjlist != nullptr, "ImageGatherManager - toObjList personnel list");
        SimpleTest::assert_true(objList.ptFaceObjlist != nullptr, "ImageGatherManager - toObjList face list");
        SimpleTest::assert_true(objList.ptWisComAcceRec != nullptr, "ImageGatherManager - toObjList access record list");
    }
}

// 测试HeartbeatManager类
void testHeartbeatManager() {
    std::cout << "\n=== Testing HeartbeatManager ===" << std::endl;

    // 测试基本功能
    {
        HeartbeatManager manager;
        SimpleTest::assert_true(!manager.isRunning(), "HeartbeatManager - initial state not running");

        bool callbackCalled = false;
        auto callback = [&callbackCalled]() -> bool {
            callbackCalled = true;
            return true;
        };

        manager.start(std::chrono::seconds(1), callback);
        SimpleTest::assert_true(manager.isRunning(), "HeartbeatManager - running after start");

        // 等待一小段时间让回调被调用
        std::this_thread::sleep_for(std::chrono::milliseconds(1100));

        manager.stop();
        SimpleTest::assert_true(!manager.isRunning(), "HeartbeatManager - stopped after stop");
        SimpleTest::assert_true(callbackCalled, "HeartbeatManager - callback was called");
    }

    // 测试间隔设置
    {
        HeartbeatManager manager;
        manager.setInterval(std::chrono::seconds(5));
        // 这个测试主要验证方法不会崩溃
        SimpleTest::assert_true(true, "HeartbeatManager - setInterval doesn't crash");
    }
}

// 测试错误处理
void testErrorHandling() {
    std::cout << "\n=== Testing Error Handling ===" << std::endl;

    // 测试ErrorHandler::errorCodeToString
    {
        std::string okStr = ErrorHandler::errorCodeToString(PUBSEC_CLT_OK);
        SimpleTest::assert_equal("Success", okStr, "ErrorHandler - PUBSEC_CLT_OK string");

        std::string errStr = ErrorHandler::errorCodeToString(PUBSEC_CLT_ERR);
        SimpleTest::assert_equal("Other error", errStr, "ErrorHandler - PUBSEC_CLT_ERR string");

        std::string unknownStr = ErrorHandler::errorCodeToString(static_cast<EPubSecCltRet>(999));
        SimpleTest::assert_equal("Unknown error", unknownStr, "ErrorHandler - unknown error code");
    }

    // 测试异常类
    {
        try {
            throw PubsecMemoryException("Test memory error");
        } catch (const PubsecException& e) {
            SimpleTest::assert_true(e.getErrorCode() == PUBSEC_CLT_NO_MEMORY, "PubsecMemoryException - error code");
            SimpleTest::assert_true(std::string(e.what()).find("Test memory error") != std::string::npos,
                                  "PubsecMemoryException - error message");
        }
    }

    // 测试参数检查宏
    {
        bool exceptionThrown = false;
        try {
            PUBSEC_CHECK_PARAM(false, "Test parameter check");
        } catch (const PubsecInvalidParameterException& e) {
            exceptionThrown = true;
        }
        SimpleTest::assert_true(exceptionThrown, "PUBSEC_CHECK_PARAM - throws exception on false condition");
    }
}

// 测试Result类
void testResult() {
    std::cout << "\n=== Testing Result Class ===" << std::endl;

    // 测试成功结果
    {
        auto successResult = Result<int>::success(42);
        SimpleTest::assert_true(successResult.isSuccess(), "Result - success result isSuccess");
        SimpleTest::assert_true(!successResult.isError(), "Result - success result not isError");
        SimpleTest::assert_true(successResult.getValue() == 42, "Result - success result getValue");
    }

    // 测试错误结果
    {
        auto errorResult = Result<int>::error(PUBSEC_CLT_ERR, "Test error");
        SimpleTest::assert_true(!errorResult.isSuccess(), "Result - error result not isSuccess");
        SimpleTest::assert_true(errorResult.isError(), "Result - error result isError");
        SimpleTest::assert_true(errorResult.getErrorCode() == PUBSEC_CLT_ERR, "Result - error result getErrorCode");
        SimpleTest::assert_equal("Test error", errorResult.getErrorMessage(), "Result - error result getErrorMessage");
    }

    // 测试错误结果访问值时抛出异常
    {
        auto errorResult = Result<int>::error(PUBSEC_CLT_ERR, "Test error");
        bool exceptionThrown = false;
        try {
            errorResult.getValue();
        } catch (const std::runtime_error& e) {
            exceptionThrown = true;
        }
        SimpleTest::assert_true(exceptionThrown, "Result - error result getValue throws exception");
    }
}

// 内存泄漏测试（简单版本）
void testMemoryManagement() {
    std::cout << "\n=== Testing Memory Management ===" << std::endl;

    // 测试大量分配和释放
    {
        for (int i = 0; i < 100; ++i) {
            ImageGatherManager manager(10, 10, 10, 10);
            for (size_t j = 0; j < manager.getPersonnelCount(); ++j) {
                manager.allocateSubImages(j, 5);
            }
            // manager会在作用域结束时自动释放所有内存
        }
        SimpleTest::assert_true(true, "Memory management - multiple allocations and deallocations");
    }

    // 测试嵌套RAII对象
    {
        {
            ImageGatherManager manager(1, 1, 0, 0);
            SubImageListManager* subManager = manager.allocateSubImages(0, 3);

            // 设置一些数据
            for (size_t i = 0; i < subManager->size(); ++i) {
                SafeStringUtils::safeFormat((*subManager)[i].achImageID, "image_%zu", i);
            }
        } // 所有对象应该在这里自动释放

        SimpleTest::assert_true(true, "Memory management - nested RAII objects cleanup");
    }
}

int main() {
    std::cout << "=== Pubsec RAII Unit Tests ===" << std::endl;

    try {
        testSafeStringUtils();
        testSubImageListManager();
        testImageGatherManager();
        testHeartbeatManager();
        testErrorHandling();
        testResult();
        testMemoryManagement();

        SimpleTest::printSummary();

        // 返回失败测试的数量
        return SimpleTest::totalTests - SimpleTest::passedTests;

    } catch (const std::exception& e) {
        std::cerr << "Unexpected exception: " << e.what() << std::endl;
        return -1;
    }
}